<?php

namespace App\Helpers;

use App\Services\InventoryPermissionService;
use App\Models\User;

class InventoryPermissionHelper
{
    protected static $permissionService;

    /**
     * Get the permission service instance
     */
    protected static function getPermissionService(): InventoryPermissionService
    {
        if (!static::$permissionService) {
            static::$permissionService = app(InventoryPermissionService::class);
        }
        
        return static::$permissionService;
    }

    /**
     * Check if current user can view inventory dashboard
     */
    public static function canViewDashboard(): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->canViewDashboard($user) : false;
    }

    /**
     * Check if current user can configure inventory
     */
    public static function canConfigureInventory(): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->canConfigureInventory($user) : false;
    }

    /**
     * Check if current user can manage allocation
     */
    public static function canManageAllocation(): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->canManageAllocation($user) : false;
    }

    /**
     * Check if current user can create trays
     */
    public static function canCreateTray(): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->canCreateTray($user) : false;
    }

    /**
     * Check if current user can create slots
     */
    public static function canCreateSlot(): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->canCreateSlot($user) : false;
    }

    /**
     * Check if current user can allocate inventory
     */
    public static function canAllocateInventory(): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->canAllocateInventory($user) : false;
    }

    /**
     * Check if current user can adjust inventory
     */
    public static function canAdjustInventory(): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->canAdjustInventory($user) : false;
    }

    /**
     * Get all inventory permissions for current user
     */
    public static function getUserPermissions(): array
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->getUserInventoryPermissions($user) : [];
    }

    /**
     * Check if current user has any inventory permission
     */
    public static function hasAnyInventoryPermission(): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->hasAnyInventoryPermission($user) : false;
    }

    /**
     * Check if current user can update a specific tray
     */
    public static function canUpdateTray($tray): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->canUpdateTray($user, $tray) : false;
    }

    /**
     * Check if current user can delete a specific tray
     */
    public static function canDeleteTray($tray): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->canDeleteTray($user, $tray) : false;
    }

    /**
     * Check if current user can update a specific slot
     */
    public static function canUpdateSlot($slot): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->canUpdateSlot($user, $slot) : false;
    }

    /**
     * Check if current user can delete a specific slot
     */
    public static function canDeleteSlot($slot): bool
    {
        $user = auth()->user();
        return $user ? static::getPermissionService()->canDeleteSlot($user, $slot) : false;
    }
}