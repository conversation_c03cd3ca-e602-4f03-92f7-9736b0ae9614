# Inventory Management Permissions System

## Overview

The inventory management system implements a comprehensive permission-based access control system that ensures users can only access inventory features they are authorized for, and only for their assigned branch.

## Components

### 1. Middleware

#### InventoryAccess Middleware
- **Purpose**: Controls access to inventory routes based on permissions
- **Usage**: `middleware('inventory.access:Permission Name')`
- **Features**:
  - Checks user authentication
  - Verifies user has assigned branch
  - Validates specific permission if provided
  - Adds branch_id to request for automatic filtering

#### BranchDataIsolation Middleware
- **Purpose**: Ensures users can only access data from their assigned branch
- **Usage**: `middleware('branch.isolation')`
- **Features**:
  - Validates user has branch assignment
  - Adds user_branch_id to request
  - Works with BranchScoped trait for automatic filtering

### 2. Permissions

The system defines the following inventory-specific permissions:

- **View Inventory Dashboard**: Access to inventory overview
- **Configure Inventory**: Manage tray and slot configuration
- **Manage Inventory Allocation**: Access to allocation management interface
- **Create Tray**: Create new trays
- **Update Tray**: Modify existing trays
- **Delete Tray**: Remove trays (only if no occupied slots)
- **Create Slot**: Create new slots
- **Update Slot**: Modify existing slots
- **Delete Slot**: Remove slots (only if not occupied)
- **Allocate Inventory**: Assign menu items to slots
- **Adjust Inventory**: Modify inventory quantities

### 3. Services

#### InventoryPermissionService
Provides centralized permission checking logic:

```php
$service = new InventoryPermissionService();

// Check specific permissions
$canView = $service->canViewDashboard($user);
$canConfigure = $service->canConfigureInventory($user);

// Check object-specific permissions
$canUpdateTray = $service->canUpdateTray($user, $tray);
$canDeleteSlot = $service->canDeleteSlot($user, $slot);

// Get all permissions for a user
$permissions = $service->getUserInventoryPermissions($user);
```

### 4. Traits

#### BranchScoped Trait
Automatically filters model queries by user's branch:

```php
use App\Traits\BranchScoped;

class Tray extends BaseModel
{
    use BranchScoped;
}

// Automatically filters by authenticated user's branch
$trays = Tray::all(); // Only returns trays from user's branch
```

## Route Protection

Routes are protected using middleware:

```php
Route::prefix('inventory')->middleware(['branch.isolation'])->group(function () {
    Route::get('/', function () {
        return view('inventory.index');
    })->middleware('inventory.access:View Inventory Dashboard');
    
    Route::get('/configuration', function () {
        return view('inventory.configuration');
    })->middleware('inventory.access:Configure Inventory');
});
```

## Component Protection

Livewire components check permissions in their mount methods:

```php
public function mount(Branch $branch)
{
    $permissionService = app(InventoryPermissionService::class);
    if (!$permissionService->canViewDashboard(auth()->user())) {
        abort(403, 'Insufficient permissions');
    }
    
    if (auth()->user()->branch_id !== $branch->id) {
        abort(403, 'Access denied to this branch');
    }
}
```

## Role-Based Access

Different roles have different permission sets:

- **Admin/Branch Head**: All inventory permissions
- **Manager**: Most permissions except deletion of trays/slots with data
- **Staff**: Basic view and allocation permissions only

## Data Isolation

The system ensures branch-specific data isolation through:

1. **Middleware**: Validates user branch assignment
2. **Global Scopes**: Automatically filter queries by branch
3. **Permission Checks**: Verify user can access specific branch data
4. **Service Layer**: Additional validation in business logic

## Testing

Comprehensive tests cover:

- Middleware functionality
- Permission service logic
- Route access control
- Component permission checks
- Data isolation
- Integration scenarios

## Usage Examples

### Checking Permissions in Controllers

```php
public function index()
{
    $permissionService = app(InventoryPermissionService::class);
    
    if (!$permissionService->canViewDashboard(auth()->user())) {
        abort(403);
    }
    
    // Controller logic...
}
```

### Conditional UI Elements

```blade
@if(app(App\Services\InventoryPermissionService::class)->canConfigureInventory(auth()->user()))
    <a href="{{ route('inventory.configuration') }}">Configure Inventory</a>
@endif
```

### Service Layer Validation

```php
public function createTray(array $data)
{
    $permissionService = app(InventoryPermissionService::class);
    
    if (!$permissionService->canCreateTray(auth()->user())) {
        throw new UnauthorizedException('Cannot create tray');
    }
    
    // Business logic...
}
```

## Migration and Setup

1. Run migrations to add Inventory module and permissions
2. Run seeder to assign permissions to existing roles
3. Middleware is automatically registered in bootstrap/app.php
4. Routes are protected with appropriate middleware

## Security Considerations

- All inventory routes require authentication
- Users must have assigned branch to access inventory
- Permissions are checked at multiple layers (route, component, service)
- Data is automatically filtered by branch
- Object-level permissions prevent cross-branch access
- Deletion permissions include business rule validation