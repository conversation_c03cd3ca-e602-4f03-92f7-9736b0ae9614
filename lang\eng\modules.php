<?php

return [
    'menu' => [
        'addMenu' => 'Add Menu',
        'menuName' => 'Menu Name',
        'allMenus' => 'Menus',
        'chooseMenu' => 'Choose Menu',
        'allMenuItems' => 'Menu Items',
        'menuNameHelp' => 'Enter the menu name below to create a new menu.',
        'item' => 'Item(s)',
        'addMenuItem' => 'Add Item',
        'editMenuItem' => 'Edit Item',
        'itemCategory' => 'Item Category',
        'addItemCategory' => 'Add Item Category',
        'typeVeg' => 'Veg',
        'addItem' => 'Add Item',
        'typeNonVeg' => 'Non Veg',
        'typeEgg' => 'Egg',
        'typeDrink' => 'Drink',
        'typeOther' => 'Other',
        'veg' => 'Veg',
        'non-veg' => 'Non Veg',
        'egg' => 'Contains Egg',
        'itemImage' => 'Item Image',
        'hasVariations' => 'Has Variations',
        'variationName' => 'Variation Name',
        'addVariations' => 'Add Variations',
        'menuItemHelp' => 'Fill in the details below to add a new item.',
        'itemName' => 'Item Name',
        'itemDescription' => 'Item Description',
        'categoryName' => 'Item Category Name',
        'setPrice' => 'Price',
        'showVariations' => 'Show Variations',
        'itemVariations' => 'Item Variations',
        'deleteMenuItem' => 'Delete Item',
        'deleteItemCategory' => 'Delete Item Category',
        'deleteVariant' => 'Delete Variant',
        'deleteMenu' => 'Delete Menu',
        'deleteVariantMessage' => 'Are you sure you want to delete the item variant?',
        'deleteMenuItemMessage' => 'Are you sure you want to delete the item?',
        'deleteMenuMessage' => 'Are you sure you want to delete the menu?',
        'deleteCategoryMessage' => 'Are you sure you want to delete the item category?',
        'filterType' => 'Filter by Type',
        'filterCategory' => 'Filter by Category',
        'itemType' => 'Item Type',
        'addItem' => 'Add Item',
        'preparationTime' => 'Preparation Time',
        'minutes' => 'Minutes',
        'browseMenu' => 'Browse Menu',
        'isAvailable' => 'Is Available',
        'filterAvailability' => 'Filter by Availability',
        'itemAvailability' => 'Item Availability',
        'available' => 'Available',
        'notAvailable' => 'Not Available',
        'drink' => 'Drink',
        'addCustomer' => 'Add Customer',
        'translations' => 'Translations',
        'notTranslated' => 'Not Translated',
        'selectLanguage' => 'Select Language',
        'showMore' => 'Show More',
        'showLess' => 'Show Less',
        'kitchenType' => 'Kitchen Type',
        'SelectKitchenType' => 'Select Kitchen Type',
        'showOnCustomerSite' => 'Show on Customer Site',
        'sortManager' => 'Menu Organizer',
        'sortMenuItems' => 'Organize Items',
        'sortManagerInfoMessage' => 'Drag and drop to organize items and categories. Click a category to filter and focus your item list.',
    ],


    'order' => [
        'qty' => 'QTY',
        'price' => 'Price',
        'amount' => 'Amount',
        'balanceReturn' => 'Balance Returned',
        'noOfPax' => 'Pax',
        'selectWaiter' => 'Select Waiter',
        'selectDeliveryExecutive' => 'Select Delivery Executive',
        'totalItem' => 'Item(s)',
        'subTotal' => 'Sub Total',
        'total' => 'Total',
        'totalOrder' => 'Total Orders',
        'orderNumber' => 'Order',
        'select' => 'Select',
        'setTable' => 'Assign Table',
        'kot' => 'KOT',
        'kotAndPrint' => 'KOT & Print',
        'print_kot' => 'Print KOT',
        'newKot' => 'New KOT',
        'bill' => 'BILL',
        'draft' => 'Waiting KOT',
        'billed' => 'Billed',
        'paid' => 'Paid',
        'payment_due' => 'Payment Due',
        'canceled' => 'Canceled',
        'infodraft' => 'Not sent to Kitchen',
        'infokot' => 'Cooking Now',
        'infobilled' => 'Waiting for Payment',
        'infopaid' => 'Payment Done',
        'infopayment_due' => 'Payment is Pending',
        'infocanceled' => 'Order Canceled',
        'addCustomerDetails' => 'Add Customer Details',
        'addPayment' => 'Add Payment',
        'addItems' => 'Add Items',
        'waiter' => 'Waiter',
        'in_kitchen' => 'In Kitchen',
        'start_cooking' => 'Start Cooking',
        'food_ready' => 'Food is Ready',
        'pending_confirmation' => 'Pending Confirmation',
        'served' => 'Food is Served',
        'addNote' => 'Add Note',
        'orderNote' => 'Order Note',
        'cancelKot' => 'Cancel KOT',
        'cancelKotMessage' => 'This will cancel the KOT.',
        'showOrder' => 'Show Order',
        'cash' => 'Cash',
        'card' => 'Card',
        'upi' => 'UPI',
        'due' => 'Due',
        'other' => 'Other',
        'payViaCash' => 'Pay via Cash',
        'payViacard' => 'Pay via Card',
        'payViaupi' => 'Pay via UPI',
        'returnAmount' => 'Amount to be returned',
        'payableAmount' => 'Payable Amount',
        'dueAmount' => 'Due Amount',
        'paymentMethod' => 'Payment Method',
        'method' => 'Method',
        'taxes' => 'Taxes',
        'viewCart' => 'View Cart',
        'placeOrder' => 'Place Order',
        'myOrders' => 'My Orders',
        'includeTax' => 'Includes Taxes',
        'payNow' => 'Pay Now',
        'payLater' => 'Pay Later',
        'payViaQr' => 'Pay via QR Code',
        'chooseGateway' => 'Choose payment gateway',
        'razorpay' => 'Razorpay',
        'stripe' => 'Stripe',
        'offline' => 'Offline',
        'flutterwave' => 'Flutterwave',
        'transactionId' => 'Transaction ID',
        'cancelOrder' => 'Cancel Order',
        'cancelOrderMessage' => 'This will cancel the order and delete any associated payments. Are you sure you want to proceed?',
        'activeOrder' => 'Active Orders',
        'todayOrder' => 'Today Orders',
        'dine_in' => 'Dine In',
        'delivery' => 'Delivery',
        'pickup' => 'Pickup',
        'out_for_delivery' => 'Out For Delivery',
        'infoout_for_delivery' => 'Order is out for delivery',
        'delivered' => 'Delivered',
        'infodelivered' => 'Order is Delivered',
        'newOrder' => 'New Order',
        'splitBill' => 'Split Bill',
        'fullPayment' => 'Full Payment',
        'splitByItems' => 'Split by Items',
        'availableItems' => 'Available Items',
        'clickToAdd' => 'Click item to add to split',
        'itemsInSplit' => 'Items in Split',
        'noItemsInSplit' => 'No items added to this split yet',
        'newSplit' => 'New Split',
        'changeMethod' => 'Change Method',
        'equalSplit' => 'Equal Split',
        'splitEqually' => 'Split equally',
        'customSplit' => 'Custom Split',
        'splitByAmount' => 'Split by amount',
        'splitByDishes' => 'Split by dishes',
        'base' => 'Base',
        'tax' => 'Tax',
        'payment' => 'Payment',
        'orderHash' => 'Order #',
        'totalBill' => 'Total Bill',
        'amountPaid' => 'Amount Paid',
        'splitAmount' => 'Split Amount',
        'remaining' => 'Remaining',
        'quantity' => 'Quantity',
        'remove' => 'Remove',
        'completePayment' => 'Complete Payment',
        'createBillAndPrintReceipt' => 'Bill & Print',
        'billAndPayment' => 'Bill & Payment',
        'discount' => 'Discount',
        'addDiscount' => 'Add Discount',
        'discountType' => 'Discount Type',
        'fixed' => 'Fixed',
        'percent' => 'Percent',
        'discountValue' => 'Discount Value',
        'enterDiscountValue' => 'Enter Discount Value',
        'calculatedDiscount' => 'Calculated Discount',
        'noOrderType' => 'Orders Disabled',
        'enableOrderType' => 'You need to enable orders in settings to start taking orders',
        'enableOrderTypeButton' => 'Enable Orders',
        'deleteOrder' => 'Delete Order',
        'deleteOrderMessage' => 'Are you sure you want to delete the order?',
        'deliveryExecutive' => 'Delivery Executive',
        'selectOrderTypes' => 'Select Order Types',
        'extraCharges' => 'Charges',
        'orderItems' => 'Order Items',
        'priceDetails' => 'Price Details',
        'paymentInformation' => 'Payment Information',
        'paymentPending' => 'Payment Pending',
        'addTip' => 'Add Tip',
        'tipAdded' => 'Tip Added',
        'tip' => 'Tip',
        'tipAmount' => 'Tip Amount',
        'tipNote' => 'Tip Note',
        'currentTotal' => 'Current Total',
        'newTotal' => 'New Total',
        'suggestedTip' => 'Suggested Tip',
        'customAmount' => 'Custom Amount',
        'continueOrder' => 'Continue Ordering',
        'orderStatus' => 'Set Order Status',
        'infoready' => 'Food is Ready',
        'infodefault' => 'Unknown Status',
        'selectStatus' => 'Select Status',
        'Order Placed' => 'Order Placed',
        'Order Confirmed' => 'Order Confirmed',
        'Order Preparing' => 'Order Preparing',
        'Order is Ready for Pickup' => 'Order is Ready for Pickup',
        'Order is Out for Delivery' => 'Order is Out for Delivery',
        'Order Served' => 'Order Served',
        'Delivered' => 'Delivered',
        'moveTo' => 'Move to',
        'info_placed' => 'Order Placed',
        'info_confirmed' => 'Order Confirmed',
        'info_preparing' => 'Order Preparing',
        'info_ready_for_pickup' => 'Order is Ready for Pickup',
        'info_out_for_delivery' => 'Order is Out for Delivery',
        'info_served' => 'Order Served',
        'info_delivered' => 'Delivered',
        'info_cancelled' => 'Order Cancelled',
        'orderCancelledMessage' => 'This Order is cancelled and cannot be modified',
        'others' => 'Others',
        'pending_verification' => 'Payment Verification',
        'pendingPaymentVerification' => 'Payment Verification Pending',
        'confirmPayment' => 'Confirm Payment',
        'reportUnpaid' => 'Report Unpaid',
        'others' => 'Others',
        'viewOnMap' => 'View on Map',
        'estimatedDeliveryTime' => 'Estimated Delivery Time',
        'deliveryFee' => 'Delivery Fee',
        'kotTicket' => 'KOT Ticket',
        'kitchenOrderTicket' => 'Kitchen Order Ticket',
        'qty' => 'Qty',
        'specialInstructions' => 'Special Instructions',
        'order' => 'Order',
        'paypal' => 'PayPal',
        'updateCustomerDetails' => 'Update Customer Details',
        'markAsReady' => 'Mark Ready',
        'itemReady' => 'Item Ready',
        'itemCooking' => 'Item Cooking',
        'payfast' => 'PayFast',
        'paypal' => 'PayPal',
        'all' => 'All',
        'paymentDetails' => 'Payment Details',
        'paystack' => 'Paystack',
    ],

    'customer' => [
        'addCustomer' => 'Add Customer',
        'name' => 'Customer Name',
        'phone' => 'Phone',
        'email' => 'Email Address',
        'customerAddress' => 'Customer Address',
        'address' => 'Address',
        'enterName' => 'Please enter your name',
        'enterCustomerName' => 'Please enter customer name',
        'editCustomer' => 'Edit Customer',
        'deleteCustomer' => 'Delete Customer',
        'deleteWithOrder' => 'Delete with their Orders',
        'deleteCustomerMessage' => 'Are you sure you want to delete the customer?',
        'customer' => 'Customer',
        'importCustomer' => 'Import Customer',
        'totalAmountReceived' => 'Total Amount Received',
    ],

    'settings' => [
        'appSettings' => 'App Settings',
        'restaurantInformation' => 'Company Information',
        'restaurantName' => 'Company Name',
        'restaurantPhoneNumber' => 'Company Phone Number',
        'restaurantEmailAddress' => 'Company Email Address',
        'restaurantAddress' => 'Company Address',
        'generalHelp' => 'Enter the general information about your Company.',
        'countryTimezone' => 'Company\'s Country, Timezone & Currency',
        'restaurantCountry' => 'Country',
        'restaurantTimezone' => 'Time Zone',
        'showTax' => 'Show Tax Id on Orders',
        'addMore' => 'Add More',
        'addTax' => 'Add Tax',
        'restaurantCurrency' => 'Currency',
        'themeColor' => 'Theme Color',
        'themeSettings' => 'Theme Settings',
        'restaurantLogo' => 'Restaurant Logo',
        'uploadLogo' => 'Upload Logo',
        'removeLogo' => 'Remove Logo',
        'paymentgatewaySettings' => 'Payment Gateways',
        'paymentHelp' => 'Enter payment gateway credentials to receive order payments.',
        'paymentHelpSuperadmin' => 'Enter payment gateway credentials to receive subscription payments.',
        'enableRazorpay' => 'Enable Razorpay',
        'enableStripe' => 'Enable Stripe',
        'enableFlutterwave' => 'Enable Flutterwave',
        'enablePaypal' => 'Enable Paypal',
        'enablePaystack' => 'Enable Paystack',
        'enableOfflinePayment' => 'Pay Offline',
        'enableQrPayment' => 'Pay Via Qr Code',
        'restaurantSettings' => 'Account Information',
        'taxSettings' => 'Taxes',
        'currencySettings' => 'Currencies',
        'offline_payment_status' => 'Enable Offline Payment',
        'roleSettings' => 'Staff Roles',
        'reservationSettings' => 'Reservation Settings',
        'languageSettings' => 'Language Settings',
        'currencySymbol' => 'Currency Symbol',
        'currencyCode' => 'Currency Code',
        'editCurrency' => 'Edit Currency',
        'addCurrency' => 'Add Currency',
        'deleteCurrency' => 'Delete Currency',
        'deleteCurrencyMessage' => 'Are you sure you want to delete the Currency?',
        'deleteTaxMessage' => 'Are you sure you want to delete the Tax?',
        'taxName' => 'Tax Name',
        'taxPercent' => 'Tax Percent',
        'taxId' => 'Tax ID',
        'addTax' => 'Add Tax',
        'deleteTax' => 'Delete Tax',
        'editTax' => 'Edit Tax',
        'emailSettings' => 'Email Settings',
        'mailFromName' => 'Mail From Name',
        'mailFromEmail' => 'Mail From Email',
        'enableQueue' => 'Enable Email Queue',
        'mailDriver' => 'Mail Driver',
        'smtpHost' => 'SMTP Host',
        'smtpPort' => 'SMTP Port',
        'smtpEncryption' => 'SMTP Mail Encryption',
        'mailUsername' => 'Mail Username',
        'mailPassword' => 'Mail Password',
        'notificationSettings' => 'Notification Settings',
        'branchSettings' => 'Vending Machine Settings',
        'addBranch' => 'Add Vending Machine',
        'editBranch' => 'Edit Vending Machine',
        'branchName' => 'Vending Machine Name',
        'branchAddress' => 'Vending Machine Address',
        'deleteBranch' => 'Delete Vending Machine',
        'branches' => 'Machines',
        'deleteBranchMessage' => 'Are you sure you want to delete the Machine? All the orders, staff members and payments related to this Machine will be deleted.',
        'upgradeLicense' => 'Upgrade Plan',
        'licenseType' => 'License Type',
        'upgradeLicenseInfo' => 'Simple, transparent pricing with everything you need to run your Company smoothly.',
        'pricing' => 'Pricing',
        'payNow' => 'Pay Now',
        'paymentSource' => 'Payment Source',
        'appName' => 'App Name',
        'paymentUpgradeHeading' => 'Upgrade to Accept Payments Seamlessly',
        'paymentUpgradeInfo' => 'Take your Company\'s efficiency to the next level by integrating Payment Gateways. Upgrade now to offer your customers secure, fast, and convenient payment options like Telebirr & Cbe',
        'themeUpgradeHeading' => 'Upgrade for a Personalized Brand Experience',
        'themeUpgradeInfo' => 'Stand out with your unique brand identity! Upgrade now to customize your Theme Colors and add your Business Logo, creating a seamless experience that reflects your Company\'s personality.',
        'manageTranslations' => 'Manage Translations',
        'customerLoginRequired' => 'Customer need to login to place order?',
        'customerName' => 'Show Customer Name',
        'customerAddress' => 'Show Customer Address',
        'tableNumber' => 'Table no.',
        'paymentQrCode' => 'Show QR code    ',
        'waiter' => 'Show Waiter Name',
        'totalGuest' => 'Show Total guest',
        'restaurantLogo' => 'Show Company Logo',
        'restaurantTax' => 'Show Company Tax',
        'noTaxFound' => 'No Tax Found',
        'selectEnvironment' => 'Select Environment',
        'enablePushNotification' => 'Enable Push Notifications',
        'pushNotificationSettings' => 'Push Notification Settings',
        'aboutUsSettings' => 'About Us',
        'customerSiteSettings' => 'Customer Site',
        'receiptSetting' => 'Receipt Settings',
        'allowCustomerDeliveryOrders' => 'Allow Customer to place Delivery Orders',
        'allowCustomerOrders' => 'Allow Customer to place Orders',
        'allowCustomerPickupOrders' => 'Allow Customer to place Pickup Orders',
        'disableLandingSite' => 'Disable Landing Site',
        'disableLandingSiteHelp' => 'Disable the landing site or set your custom landing site.',
        'disableLandingSiteHelpDescription' => 'When enabled, this will disable the front website and redirect all visitors directly to the login page.',
        'landingSiteType' => 'Landing Site Type',
        'landingSiteUrl' => 'Landing Site URL',
        'theme' => 'Theme',
        'custom' => 'Custom',
        'billing' => 'Billing',
        'package' => 'Package',
        'isWaiterRequestEnabled' => 'Enable Waiter Request',
        'dineInOnlinePaymentRequired' => 'Dine-in: Online payment required',
        'deliveryOnlinePaymentRequired' => 'Delivery: Online payment required',
        'pickupOnlinePaymentRequired' => 'Pickup: Online payment required',
        'enableRazorpayOrStripe' => 'Please enable at least one payment gateway (e.g., Razorpay, Stripe, Flutterwave, PayPal, Paystack, Payfast) to configure online payment options.',
        'generalSettingsUseInfo' => 'Enabling this allows only online payments via the active gateway. If disabled, both online and cash payments are accepted with an active gateway.',
        'defaultReservationStatus' => 'Default Table Reservation Status',
        'reservationStatusConfirmed' => 'Confirmed',
        'reservationStatusCheckedIn' => 'Checked In',
        'reservationStatusCancelled' => 'Cancelled',
        'reservationStatusPending' => 'Pending',
        'reservationStatusNoShow' => 'No Show',
        'receipt' => 'receipt Setting',
        'facebook_link' => 'Facebook Link',
        'instagram_link' => 'Instagram Link',
        'twitter_link' => 'Twitter Link',
        'yelp_link' => 'Yelp Link',
        'tableRequiredDineIn' => 'Table Required for Dine-In',
        'allowDineIn' => 'Allow Dine-In',
        'defaultLanguage' => 'Default Language',
        'defaultCurrency' => 'Default Currency',
        'storageSettings' => 'Storage Settings',
        'selectStorage' => 'Select Storage',
        'localStorageNote' => 'Local storage is the default storage option. It stores files on the server\'s local disk.',
        'storageSuggestion' => '<b>Recommendation:</b> Consider using <a href="https://digitalocean.pxf.io/froiden" class="underline" target="_blank">DigitalOcean Spaces</a>, <a href="https://aws.amazon.com/s3" class="underline" target="_blank">AWS S3</a>, <a href="https://wasabi.com" class="underline" target="_blank">Wasabi</a> or <a href="https://min.io/" class="underline" target="_blank">MinIO</a> Storage for an additional layer of security',
        'digitaloceanAccessKey' => 'DigitalOcean Access Key',
        'digitaloceanSecretKey' => 'DigitalOcean Secret Key',
        'digitaloceanBucket' => 'DigitalOcean Bucket',
        'digitaloceanRegion' => 'DigitalOcean Region',
        'minioAccessKey' => 'Minio Access Key',
        'minioSecretKey' => 'Minio Secret Key',
        'minioBucket' => 'Minio Bucket',
        'minioRegion' => 'Minio Region',
        'minioEndpoint' => 'Minio Endpoint',
        'wasabiAccessKey' => 'Wasabi Access Key',
        'wasabiSecretKey' => 'Wasabi Secret Key',
        'wasabiBucket' => 'Wasabi Bucket',
        'wasabiRegion' => 'Wasabi Region',
        'awsAccessKey' => 'AWS Key ID',
        'awsSecretKey' => 'AWS Access Key',
        'awsBucket' => 'AWS Bucket',
        'awsRegion' => 'AWS Region',
        'testStorage' => 'Test Storage',
        'testStorageFile' => 'Upload file to test if it gets uploaded to bucket',
        'moveFilesToCloud' => 'Move Files to Cloud',
        'local' => 'Local',
        'digitalocean' => 'DigitalOcean',
        'aws_s3' => 'AWS S3',
        'wasabi' => 'Wasabi',
        'minio' => 'MinIO',
        'currencyFormat' => 'Currency Format',
        'currencyPosition' => 'Currency Position',
        'thousandSeparator' => 'Thousand Separator',
        'decimalSeparator' => 'Decimal Separator',
        'numberOfdecimals' => 'Number of Decimals',
        'left' => 'Left',
        'right' => 'Right',
        'leftWithSpace' => 'Left with Space',
        'rightWithSpace' => 'Right with Space',
        'deleteTax' => 'Are you Sure, you wan to delete this Tax?',
        'deleteTaxMessage' => 'Delete tax permanently',
        'showLogoText' => 'Show Company Name with Logo',
        'socialMediaLinks' => 'Social Media Links',
        'facebook' => 'Facebook',
        'instagram' => 'Instagram',
        'twitter' => 'Twitter',
        'facebookPlaceHolder' => 'Enter your Facebook URL',
        'instagramPlaceHolder' => 'Enter your Instagram URL',
        'twitterPlaceHolder' => 'Enter your Twitter URL',
        'allowCustomerPickupOrdersDescription' => 'Enable this to allow customers to place pickup orders.',
        'allowCustomerDeliveryOrdersDescription' => 'Enable this to allow customers to place delivery orders.',
        'allowCustomerOrdersDescription' => 'Enable this to allow customers to place orders.',
        'customerLoginRequiredDescription' => 'Enable this to require customers to login before placing orders.',
        'orderSettings' => 'Order Settings',
        'orderSettingsDescription' => 'Configure settings related to customer orders.',
        'customerSiteSettingsDescription' => 'Configure settings related to the customer site.',
        'qrCodeImage' => 'Upload QR Code Image',
        'qrCodeRequirements' => 'Upload a QR code image for payment. The image should be a clear, high-quality image of the QR code.',
        'qrPaymentDescription' => 'Enable this to allow customers to pay via QR code.',
        'uploadQrCode' => 'Upload QR Code',
        'logoDescription' => 'Upload a logo for your Company.',
        'showLogoTextDescription' => 'Enable this to show the Company name with the logo.',
        'customerInformation' => 'Customer Information',
        'receiptPreview' => 'Receipt Preview',
        'previewReceipt' => 'Preview Receipt',
        'orderDetails' => 'Order Details',
        'cronJobSetting' => 'Cron Job Setting',
        'customModules' => 'Custom Modules',
        'installCustomModule' => 'Install Custom Module',
        'enablePayViaCash' => 'Pay Via Cash',
        'seo' => 'SEO',
        'metaKeyword' => 'Meta Keyword',
        'metaDescription' => 'Meta Description',
        'offlinePaymentDescription' => 'Enable this to allow customers to pay via offline payment methods.',
        'offlinePaymentDetailsDescription' => 'Enable this to allow customers to pay via offline payment methods.',
        'offlinePaymentDetails' => 'Offline Payment Details',
        'cashPaymentDescription' => 'Enable this to allow customers to pay via cash payment methods.',
        'restaurantRequiresApproval' => 'Account Requires Approval',
        'restaurantRequiresApprovalInfo' => 'Enable this to require admin approval for new company registrations.',
        'themeColorDescription' => 'Select the theme color for your company.',
        'upload_fav_icon_android_chrome_192' => 'Upload Favicon for Android Chrome (192x192)px',
        'upload_fav_icon_android_chrome_512' => 'Upload Favicon for Android Chrome (512x512)px',
        'upload_fav_icon_apple_touch_icon' => 'Upload Favicon for Apple Touch Icon',
        'upload_favicon_16' => 'Upload Favicon (16x16)px',
        'upload_favicon_32' => 'Upload Favicon (32x32)px',
        'favicon' => 'Upload Favicon',
        'editCharge' => 'Edit Charge',
        'addCharge' => 'Add Charge',
        'chargeName' => 'Charge Name',
        'chargeType' => 'Type',
        'percent' => 'Percent',
        'fixed' => 'Fixed',
        'rate' => 'Rate',
        'orderType' => 'Order Type',
        'deleteCharge' => 'Delete Charge',
        'deleteChargeMessage' => 'Are you sure you want to delete the charge?',
        'hideTodayOrders' => 'Hide Today Orders',
        'hideTopNav' => 'Hide Top Navigation',
        'hideTodayOrdersDescription' => 'Enable this to hide today orders widget from top navigation.',
        'charges' => 'Additional Charges',
        'onDesktop' => 'On Desktop',
        'onMobile' => 'On Mobile',
        'openViaQrCode' => 'Only When Open via QR Code',
        'onDesktopDescription' => 'Enable this to allow customers to call waiters on desktop.',
        'onMobileDescription' => 'Enable this to allow customers to call waiters on mobile.',
        'openViaQrCodeDescription' => 'Enable this to allow customers to call waiters only when they open the app via QR code.',
        'favicons' => 'Favicons',
        'faviconsDescription' => 'Upload a favicon for your site.',
        'generateFavicon' => 'Generate Favicon',
        'siteWebManifest' => 'Site Web Manifest',
        'paymentDetails' => 'Payment Details',
        'paymentQrImage' => 'Payment QR Image',
        'uploadPaymentQrCode' => 'Upload Payment QR Code',
        'showPaymetQrCode' => 'Show Payment QR Code',
        'payFromYourPhone' => 'PAY FROM YOUR PHONE',
        'scanQrCode' => 'Scan the QR code to pay Your Bill ',
        'isChargeEnabled' => 'Apply Charge',
        'isChargeEnabledDescription' => 'Check to enable this charge.',
        'enableTipShop' => 'Enable Tip Customer Site',
        'enableTipShopDescription' => 'Enable this to allow customers to add tips to their orders.',
        'enableTipPos' => 'Enable Tip POS',
        'enableTipPosDescription' => 'Enable this to allow adding tips to their orders in POS.',
        'pwaSettings' => 'PWA Settings',
        'enbalePwaApp' => 'Enable PWA App',
        'enablePwadescription' => 'Enable this to allow customers to install your app on their devices.',
        'metaTitle' => 'Meta Title',
        'addMoreWebPage' => 'Add More Web Page',
        'addMoreWebPageHelp' => 'Add a Dynamic web page to your site.',
        'menuName' => 'Menu Name',
        'menuSlug' => 'Menu Slug',
        'menuContent' => 'Menu Content',
        'showMoreWebPage' => 'Show More Web Page',
        'addDyanamicMenu' => 'Add Dynamic Menu',
        'deleteDyanamicMenu' => 'Delete Dynamic Menu',
        'editDynamicMenu' => 'Edit Dynamic Menu',
        'addDynamicMenu' => 'Add Dynamic Menu',
        'isActive' => 'Active',
        'showPaymentDetails' => 'Show Payment Details',
        'autoConfirmOrders' => 'Auto Confirm Order Status',
        'autoConfirmOrdersDescription' => 'Enable this to automatically confirm order progress status.',
        'printerSetting' => 'Printer Settings',
        'printerSettingDescription' => 'Configure printer settings for your Company.',
        'headerPage' => 'Header Page',
        'headerTitle' => 'Header Title',
        'headerDescription' => 'Header Description',
        'headerImage' => 'Header Image',
        'preview' => 'Preview',
        'featureWithImage' => 'Feature With Image',
        'addFeature' => 'Add Feature',
        'editFeature' => 'Edit Feature',
        'deleteFeature' => 'Delete Feature',
        'featureTitle' => 'Feature Title',
        'featureDescription' => 'Feature Description',
        'featureImage' => 'Feature Image',
        'lanuage' => 'Language',
        'selectLanguage' => 'Select Language',
        'feature' => 'Feature',
        'featureIcon' => 'Feature Icon',
        'featureHeading' => 'Feature Heading',
        'featureTitle' => 'Feature Title',
        'featureDescription' => 'Feature Description',
        'featureImage' => 'Feature Image',
        'addFeatureWithicon' => 'Add Feature With Icon',
        'editFeatureWithicon' => 'Edit Feature With Icon',
        'featureIcon' => 'Feature Icon',
        'language' => 'Language',
        'featureWithIcon' => 'Feature With Icon',
        'editFeatureWithicon' => 'Edit Feature With Icon',
        'reviewSetting' => 'Review setting',
        'addReview' => 'Add Review',
        'editReview' => 'Edit Review',
        'deleteReview' => 'Delete Review',
        'reviewerName' => 'Reviewer Name',
        'reviewerDesignation' => 'Reviewer Designation',
        'reviews' => 'Reviews',
        'noReviews' => 'No Reviews',
        'faqSetting' => 'FAQ setting',
        'addFaq' => 'Add FAQ',
        'editFaq' => 'Edit FAQ',
        'deleteFaq' => 'Delete FAQ',
        'title' => 'Title',
        'description' => 'Description',
        'question' => 'Question',
        'answer' => 'Answer',
        'contactSetting' => 'Contact setting',
        'contact' => 'Contact',
        'contactCompany' => 'Contact Company',
        'contactImage' => 'Contact Image',
        'addContact' => 'Add Contact',
        'editContact' => 'Edit Contact',
        'deleteContact' => 'Delete Contact',
        'contactName' => 'Contact Name',
        'email' => 'Email',
        'address' => 'Address',
        'footerSetting' => 'Footer setting',
        'footerCopyrightText' => 'Footer Copyright Text',
        'addFeatureWithIcon' => 'Add Feature With Icon',
        'faq' => 'FAQ',
        'noFaq' => 'No FAQ',
        'featureHeading' => 'Feature Heading',
        'priceSetting' => 'Price setting',
        'priceTitle' => 'Price Title',
        'priceDescription' => 'Price Description',
        'staticLandingPage' => 'Static Landing Page',
        'dynamicLandingPage' => 'Dynamic Landing Page',
        'flutterwaveSettings' => 'Flutterwave Settings',
        'enableFlutterwave' => 'Enable Flutterwave',
        'flutterwaveKey' => 'Flutterwave Key',
        'flutterwaveSecret' => 'Flutterwave Secret',
        'flutterwaveEncryptionKey' => 'Flutterwave Encryption Key',
        'flutterwaveWebhookHash' => 'Flutterwave Webhook Secret Hash',
        'testFlutterwaveKey' => 'Test Flutterwave Key',
        'testFlutterwaveSecret' => 'Test Flutterwave Secret',
        'testFlutterwaveEncryptionKey' => 'Test Flutterwave Encryption Key',
        'testFlutterwaveWebhookHash' => 'Test Flutterwave Webhook Secret Hash',
        'selectEnvironment' => 'Select Environment',
        'webhookUrl' => 'Webhook URL',
        'copyWebhookUrl' => 'Copy',
        'copied' => 'Copied!',
        'save' => 'Save',
        'razorpayKey' => 'Razorpay KEY',
        'razorpaySecret' => 'Razorpay SECRET',
        'razorpayWebhookKey' => 'Razorpay Webhook Key',
        'testRazorpayKey' => 'Test Razorpay KEY',
        'testRazorpaySecret' => 'Test Razorpay SECRET',
        'testRazorpayWebhookKey' => 'Test Razorpay Webhook Key',
        'stripeKey' => 'Stripe KEY',
        'stripeSecret' => 'Stripe SECRET',
        'stripeWebhookKey' => 'Stripe Webhook Key',
        'getStripeCredentials' => 'Get Stripe Credentials',
        'getStripeTestCredentials' => 'Get Stripe Test Credentials',
        'testStripeKey' => 'Test Stripe KEY',
        'testStripeSecret' => 'Test Stripe SECRET',
        'testStripeWebhookKey' => 'Test Stripe Webhook Key',
        'flutterwavePublicKey' => 'Flutterwave Public Key',
        'flutterwaveSecretKey' => 'Flutterwave Secret Key',
        'autoConfirmOrdersDescription' => 'Enable this to automatically confirm orders status.',
        'deliverySettings' => 'Delivery Settings',
        'getGoogleMapApiKeyHelp' => 'To get a Google Maps API key, you need to create a project in the Google Cloud Console and enable the Maps JavaScript API.',
        'learnMore' => 'Learn more',
        'enablePayPal' => 'Enable PayPal',
        'enablePayfast' => 'Enable Payfast',
        'branchPhone' => 'Vending Machine Phone ',
        'sessionDriver' => 'Session Driver',
        'sessionDriverFile' => 'File',
        'sessionDriverDatabase' => 'Database',
        'selectEnvironment' => 'Select Environment',
        'paypalSandboxClientId' => 'PayPal Sandbox Client ID',
        'paypalSandboxSecret' => 'PayPal Sandbox Secret',
        'paypalLiveClientId' => 'PayPal Live Client ID',
        'paypalLiveSecret' => 'PayPal Live Secret',
        'webhookUrl' => 'Webhook URL',
        'enablePayfast' => 'Enable Payfast',
        'enableItemLevelStatus' => 'Enable Item Level Status',
        'enableItemLevelStatusDescription' => 'Enable this to allow statuses to be set at the item level.',
        'kotStatusesPendingDescription' => 'Initial status when KOT is created and waiting to be processed',
        'kotStatusesCookingDescription' => 'Status when kitchen staff is preparing the order',
        'kotStatusesReadyDescription' => 'Status when the order is completed and ready for serving',
        'kotStatusesServedDescription' => 'Status when the order is served to the customer',
        'kotStatusesCancelledDescription' => 'Status when the order is cancelled',
        'phone' => 'Phone',
        'enablePaystack' => 'Enable Paystack',
        'adminSettings' => 'Enable payment gateways to show in Account panel',

    ],

    'delivery' => [
        'useDifferentLocation' => 'Use Different Location',
        'deliverySettings' => 'Delivery Settings',
        'enableDelivery' => 'Enable Delivery',
        'maxRadius' => 'Maximum Delivery Radius',
        'kilometers' => 'Kilometers (km)',
        'miles' => 'Miles (mi)',
        'feeCalculationMethod' => 'Fee Calculation Method',
        'fixedFee' => 'Fixed Fee',
        'feePerDistance' => 'Fee per :unit',
        'distanceTiers' => 'Distance Tiers',
        'distanceTiersDescription' => 'Set different fees for different distance ranges',
        'minDistance' => 'Min Distance (:unit)',
        'maxDistance' => 'Max Distance (:unit)',
        'fee' => 'Fee',
        'addTier' => 'Add Tier',
        'freeDeliveryOptions' => 'Free Delivery Options',
        'freeDeliveryOverAmount' => 'Free Delivery Over Amount',
        'freeDeliveryWithinRadius' => 'Free Delivery Within Radius',
        'leaveEmptyToDisable' => 'Leave empty to disable this option',
        'deliverySchedule' => 'Delivery Schedule',
        'deliveryHoursStart' => 'Delivery Hours Start',
        'deliveryHoursEnd' => 'Delivery Hours End',
        'leave247Delivery' => 'Leave both fields empty or 00:00 for 24/7 delivery',
        'deliveryTimeEstimate' => 'Delivery Time Estimation',
        'avgPrepTime' => 'Average Preparation Time',
        'avgDeliverySpeed' => 'Average Speed of Delivery Rider',
        'minutes' => 'Minutes',
        'feeDetails' => 'Fee Details',
        'distanceUnit' => 'Distance Unit',
        'mapApiKey' => 'Google Map API Key',
        'branchLat' => 'Machine Latitude',
        'branchLng' => 'Machine Longitude',
        'addNewAddress' => 'Add New Address',
        'editAddress' => 'Edit Address',
        'addressLabel' => 'Address Label',
        'addressLabelPlaceholder' => 'Home, Office, etc.',
        'searchLocation' => 'Search Location',
        'searchLocationPlaceholder' => 'Enter your address or location',
        'fullAddress' => 'Full Address',
        'fullAddressPlaceholder' => 'Enter your full address details',
        'noAddressesFound' => 'No addresses found',
        'addAddressDescription' => 'Add your delivery addresses to make ordering faster',
        'saveAddress' => 'Save Address',
        'updateAddress' => 'Update Address',
        'confirmDeleteAddress' => 'Are you sure you want to delete this address?',
        'confirmDeleteAddressDescription' => 'This action cannot be undone.',
        'pleaseSelectLocation' => 'Please select a location on the map',
        'autoDetectMyLocation' => 'Detect My Location',
        'selectDeliveryLocation' => 'Select Delivery Location',
        'selectLocationDescription' => 'Choose a saved address or add a new delivery location',
        'useSavedAddress' => 'Use Saved Address',
        'addNewLocation' => 'Add New Location',
        'searchLocation' => 'Search for your location',
        'fullAddress' => 'Complete Address',
        'fullAddressPlaceholder' => 'Enter apartment, floor, landmark, etc.',
        'confirmLocation' => 'Confirm Location',
        'locationOutOfRange' => 'Location is out of delivery range',
        'deliveryAreaMap' => 'Delivery Area Map',
        'showDeliveryArea' => 'Show Delivery Area',
        'hideDeliveryArea' => 'Hide Delivery Area',
        'orderWillBeDeliveredHere' => 'Your order will be delivered here',
        'placePinAccurately' => 'Place the pin accurately on the map',
        'detectLocation' => 'Detect My Location',
        'savedAddresses' => 'Saved Addresses',
        'noSavedAddresses' => 'No saved addresses found',
        'addressLabel' => 'Address Label (e.g. Home, Office)',
        'addressDetails' => 'Address Details',
        'showDeliveryRange' => 'Show Delivery Range',
        'hideDeliveryRange' => 'Hide Delivery Range',
        'dragMarkerToAdjust' => 'Place the pin accurately on the map',
        'deliveryLocation' => 'Order will be delivered here',
        'deliveryFee' => 'Delivery Fee',
        'maxRadiusAutoSet' => 'Max Radius will be set automatically.',
        'shopLocation' => 'The location',
        'orderQualifiesForFreeDelivery' => 'Your order qualifies for free delivery',
        'freeDelivery' => 'Free Delivery',
        'changeDeliveryAddress' => 'Change Delivery Address',
        'deliveryAddress' => 'Delivery Address',
        'useCurrentLocation' => 'Use Current Location',
        'deliveryNotAvailable' => 'Delivery is not available at the moment. Please connect  directly for assistance.',
        'deliveryHours' => 'Delivery Hours',
        'nextDeliveryAt' => 'Next delivery available at :time',
        'locationOutOfRange' => 'Location is out of delivery range',
        'deliveryScheduleStart' => 'Delivery Start Time',
        'deliveryScheduleEnd' => 'Delivery End Time',
        'locationPermissionDenied' => 'Location permission denied. Please enable location access and try again.',
        'outsideDeliveryHours' => 'Delivery service available from :start to :end (:timezone). Please order during these hours.',
        'currentlyOutsideHours' => 'Currently outside delivery hours',
        '24hDelivery' => '24/7 Delivery Available',
        'deliveryTimeEstimation' => 'Estimated delivery time: :time minutes',
        'estimatedTimeUnavailable' => 'Estimated time unavailable. Please contact the Company for more details.',
        'additionalEtaBufferTime' => 'Additional Time Buffer',
        'additionalEtaBufferTimeDescription' => 'Add optional buffer time (in minutes) to cover possible delays. Default is 0 if not specified.',
    ],


    'dashboard' => [
        'todayOrderCount' => 'Today\'s Orders',
        'todayEarnings' => 'Today\'s Earnings',
        'sinceYesterday' => 'Since yesterday',
        'todayStats' => 'Statistics',
        'todayCustomerCount' => 'Today\'s Customer',
        'averageDailyEarning' => 'Average Daily Earnings',
        'sincePreviousMonth' => 'Since Previous Month',
        'earnings' => 'Earnings',
        'salesThisMonth' => 'Sales This Month',
        'topTables' => 'Top Selling Tables',
        'topDish' => 'Top Selling Dish',
        'todayRestaurantCount' => 'Today\'s Company Count',
        'totalRestaurantCount' => 'Total Company Count',
        'totalFreeRestaurantCount' => 'Total Free Company Count',
        'totalPaidRestaurantCount' => 'Total Paid Company Count',
        'verificationPendingInfo' => 'Your Company is pending verification. Please wait for admin approval.',
        'verificationPending' => 'Verification Pending',
        'verificationPendingDescription' => 'Companyis waiting for approval',
        'verificationRejectedInfo' => 'Your Company verification has been rejected. Please contact admin for more details.',
        'approvalStatus' => 'Approval Status',
        'onboarding' => 'Onboarding',
        'onboardingDescription' => 'Complete these steps to set up your application properly before using it.',
        'installation' => 'Installation',
        'installationCompleted' => 'The application has been successfully installed.',
        'smtpConfiguration' => 'SMTP Configuration',
        'smtpConfigurationDescription' => 'Configure your email settings to ensure all email notifications work properly.',
        'cronJobConfiguration' => 'CRON Job Configuration',
        'cronJobConfigurationDescription' => 'Set up CRON jobs to ensure automated tasks run correctly.',
        'applicationNameChange' => 'Application Name Change',
        'applicationNameChangeDescription' => 'Customize your application name to match your brand.',
    ],

    'update' => [
        'systemDetails' => 'System Details',
        'updateTitle' => 'Update To New Version',
        'updateDatabase' => 'Update Database',
        'fileReplaceAlert' => 'To update the qrjeblo to the new version check documentation for the instructions.',
        'updateDatabaseButton' => 'Click to update database',
        'newUpdate' => 'New update available',
        'updateNow' => 'Update Now',
        'updateAlternate' => 'If the <b>Update Now</b> button does not work then follow the <b> <a href="https://froiden.freshdesk.com/support/solutions/articles/43000554421-update-application-manually" target="_blank">Manual update</a></b> instructions as mentioned in the documentation.',
        'updateManual' => 'Update Alternate Method',
        'updateFiles' => 'Update Files',
        'install' => 'Install',
        'downloadUpdateFile' => 'Download Update File',
        'moduleFile' => 'Once the zip file is uploaded, you will see a list of modules available for installation or update. To proceed with the installation or update, simply click on the "install" button for the respective module. Please note that after clicking the "install" button, you will be logged out. Once the installation is complete, you will be able to see the module on the module list. You can activate or deactivate the module from the list at any time.',
        'customModules' => 'Custom Modules',
        'customModulesDescription' => 'Custom modules are additional features that can be added to your Account. They are installed as separate modules and can be activated or deactivated at any time.',
        'customModulesList' => 'Custom Modules List',
        'installCustomModule' => 'Install Custom Module',
        'uploadModule' => 'Upload Module',
        'downloadFilefromCodecanyon' => 'Download file from Codecanyon',
        'moduleFileItem' => 'Module File Item',
        'moduleFileItemDescription' => 'Module File Item Description',
    ],

    'staff' => [
        'addStaff' => 'Add Member',
        'name' => 'Member Name',
        'email' => 'Email Address',
        'enterName' => 'Please enter your name',
        'editMember' => 'Edit Member',
        'deleteMember' => 'Delete Member',
        'deleteMemberMessage' => 'Are you sure you want to delete the member?',
        'photo' => 'Photo',
        'addExecutive' => 'Add Executive',
        'available' => 'Available',
        'on_delivery' => 'On Delivery',
        'inactive' => 'Inactive',
        'editExecutive' => 'Edit Executive',
        'password' => 'Password',
        'Branch Head' => 'Vending Machine admin',
        'Admin' => 'Admin',
        'Super Admin' => 'Super Admin',
    ],

    'reservation' => [
        'slotType' => 'Slot Type',
        'timeStart' => 'Start Time',
        'timeEnd' => 'End Time',
        'timeSlotDifference' => 'Time Slot Difference',
        'available' => 'Available',
        'unavailable' => 'Unavailable',
        'maxGuests' => 'Maximum Guests',
        'guests' => 'Guests',
        'Lunch' => 'Lunch',
        'Breakfast' => 'Breakfast',
        'Dinner' => 'Dinner',
        'No_Show' => 'No Show',
        'Checked_In' => 'Checked In',
        'Confirmed' => 'Confirmed',
        'Cancelled' => 'Cancelled',
        'Pending' => 'Pending',
        'todayReservations' => 'Today Reservations',
        'newReservations' => 'New Reservations',
        'newReservation' => 'New Reservation',
        'selectDate' => 'Select Date of Booking',
        'upgradeHeading' => 'Unlock more futurs Today!',
        'upgradeInfo' => 'Upgrade now and streamline your operations.',
    ],

    'notifications' => [
        'order_received' => 'New Order Received',
        'order_received_info' => 'Company admin will receive an email when a new order is placed by the customer.',
        'reservation_confirmed' => 'Reservation Confirmation',
        'reservation_confirmed_info' => 'Customer will receive an email after making the reservation.',
        'new_reservation' => 'New Reservation Received',
        'new_reservation_info' => 'Company admin will receive an email when a new reservation is made by the customer.',
        'order_bill_sent' => 'Order Bill',
        'order_bill_sent_info' => 'Customer will receive the order bill via email.',
        'staff_welcome' => 'Staff Welcome Email',
        'staff_welcome_info' => 'Staff Member will welcome email when you add a new staff member.',
    ],

    'onboarding' => [
        'completeSteps' => 'Complete following steps to get started',
        'markComplete' => 'Mark Complete',
        'addBranchHeading' => 'Add Vending Machine Information',
        'addBranchInfo' => 'Provide the essential details of your companys vending machines.',
        'addMenuHeading' => 'Build Your Menu',
        'addMenuItemHeading' => 'Add Items to Your Menu',
        'addMenuItemInfo' => 'Add items with their descriptions and prices .',
        'addOrderHeading' => 'Test Your Order Process',
        'addOrderInfo' => 'Make sure everything works by placing a test order for your setup.',
    ],

    'restaurant' => [
        'addRestaurant' => 'Add Company',
        'name' => 'Company Name',
        'email' => 'Email Address',
        'address' => 'Address',
        'editRestaurant' => 'Edit Company',
        'deleteRestaurant' => 'Delete Company',
        'deleteRestaurantMessage' => 'Are you sure you want to delete the Company?',
        'logo' => 'Logo',
        'phone' => 'Phone',
        'restaurantDetails' => 'Company Details',
        'restaurantBranchDetails' => 'Add Company vending machine Details',
        'changePassword' => 'Change Password',
        'firstAdmin' => 'First Admin',
        'nextBranchDetails' => 'Next: vending machine Details',
        'updatePackage' => 'Update Package',
        'needApproval' => 'Need Approval',
        'resetFilter' => 'Reset Filter',
        'rejectionReason' => 'Rejection Reason',
        'currentPackage' => 'Current Package',
        'rejectionReasonPlaceholder' => 'Provide a reason for rejection',
    ],

    'package' => [
        'packageName' => 'Package Name',
        'description' => 'Description',
        'monthlyPrice' => 'Monthly Price',
        'annualPrice' => 'Annual Price',
        'annually' => 'Annually',
        'monthly' => 'Monthly',
        'trialDays' => 'Trial Days',
        'trialStatus' => 'Trial Status',
        'NotificationBeforeDays' => 'Notification Before Days',
        'trialMessage' => 'Trial Message',
        'lifetimePrice' => 'Lifetime Price',
        'packagePrice' => 'Package Price',
        'selectBillingCycle' => 'Select Billing Cycle',
        'packageType' => 'Package Type',
        'annual' => 'Annual',
        'free' => 'Free',
        'default' => 'Default',
        'private' => 'Private',
        'trial' => 'Trial',
        'lifetime' => 'Lifetime',
        'moduleInPackage' => 'Modules in Package',
        'daysLeftTrial' => 'days left on trial',
        'trialExpired' => 'Trial expired',
        'selectPackage' => 'Select Package',
        'normal' => 'Normal',
        'trialDays' => 'Trial Days',
        'addPackage' => 'Add Package',
        'editPackage' => 'Edit Package',
        'freePlan' => 'Free Plan',
        'recommended' => 'Recommended',
        'trialPackage' => 'Trial Package',
        'monthlyRazorpayId' => 'Monthly Razorpay Id',
        'annualRazorpayId' => 'Annual Razorpay Id',
        'lifetimeRazorpayId' => 'Lifetime Razorpay Id',
        'lifetimeStripeId' => 'Lifetime Stripe Id',
        'monthlyStripeId' => 'Monthly Stripe Id',
        'annualStripeId' => 'Annual Stripe Id',
        'selectAdditionalFeature' => 'Select Additional Feature',
        'trialPackageDetails' => 'If this package is active, then the account which registers for the first time goes to this package.',
        'defaultPackage' => 'Default Package',
        'defaultPackageDetails' => 'When the trial package expires, the accountgoes back to this plan.',
        'defaultPackageDetails2' => 'When the account fails the payment of an upgraded package, the account goes back to this plan.',
        'thisPackageCannotBeDeleted' => 'This package cannot be deleted',
        'planExpire' => 'If your plan expires you will be assigned this package.',
        'trialPeriod' => 'Trial Period',
        'days' => 'Day(s)',
        'private' => 'Private',
        'freePlan' => 'Free Plan',
        'paidPlan' => 'Paid Plan',
        'choosePackageType' => 'Choose Package Type',
        'monthlyPlan' => 'Monthly Plan',
        'choseFreePlan' => 'Choose Free Plan',
        'currentPlan' => 'Current Plan',
        'chooseDefaultPlan' => 'Choose Default Plan',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'annualPlan' => 'Annual Plan',
        'monthlyPrice' => 'Monthly Plan Price',
        'annualPrice' => 'Annual Plan Price',
        'isPrivate' => 'Make Private',
        'selectModules' => 'Select Modules for this package',
        'selectAll' => 'Select All',
        'deletePackage' => 'Delete Package',
        'deletePackageMessage' => 'Are you sure you want to delete the package?',
        'isRecommended' => 'Mark as Recommended',
        'restaurantCurrentPackage' => 'Account\'s Current Package',
        'updatePackage' => 'Update Package',
        'packageDetails' => 'Package Details',
        'transactionId' => 'Transaction ID',
        'paymentGateway' => 'Payment Gateway',
        'choosePlan' => 'Choose Plan',
        'noPaymentOptionEnable' => 'No payment options are enabled.',
        'choosePaymentMethod' => 'Choose Payment Method',
        'amount' => 'Amount',
        'paymentBy' => 'Payment By',
        'created' => 'Created',
        'status' => 'Status',
        'receipt' => 'Receipt',
        'paymentDate' => 'Payment Date',
        'payOnline' => 'Pay Online',
        'billingCycle' => 'Billing Cycle',
        'payOffline' => 'Pay Offline',
        'purchaseHistory' => 'Purchase History',
        'offlineRequest' => 'Offline Request',
        'planDetails' => 'Plan Details',
        'offlineUploadFile' => 'Please attach a file of your offline payment receipt',
        'offlineDescriptionPlaceholder' => 'Provide details of your offline payment',
        'remark' => 'Remark',
        'trialExpireOn' => 'Trial Expires On',
        'selectDate' => 'Select Date',
        'amount' => 'Amount',
        'paymentDate' => 'Payment Date',
        'nextPaymentDate' => 'Next Payment Date',
        'licenceExpiresOn' => 'License Expires On',
        'planExpire' => 'Plan Expires On',
        'defaultPlan' => 'Your default plan.',
        'packageName' => 'Package Name',
        'choosePackageType' => 'Choose Package Type',
        'payOnce' => 'Pay One Time',
        'payMonthly' => 'Pay Monthly',
        'payAnnually' => 'Pay Annually',
        'branchLimit' => 'Machine Limit',
        'branchLimitInfo' => 'The account can add a limited number of machines. Enter -1 to let unlimited machines.',
        'licenseExpiresOn' => 'License Expires On',
        'additionalFeatures' => 'Additional Features',
        'noAdditionalFeatures' => 'No additional features available.',
        'currency' => 'Currency',
        'chooseCurrency' => 'Choose Currency',
        'monthlyFlutterwaveId' => 'Monthly Flutterwave Id',
        'annualFlutterwaveId' => 'Annual Flutterwave Id',
        'monthlyPaypalId' => 'Monthly Paypal Id',
        'annualPaypalId' => 'Annual Paypal Id',
        'monthlyPaystackId' => 'Monthly Paystack Id',
        'annualPaystackId' => 'Annual Paystack Id',
    ],

    'billing' => [
        'name' => 'Name',
        'description' => 'Description',
        'deleteOfflinePaymentMethod' => 'Delete Offline Payment Method',
        'askDeleteOfflinePaymentMethod' => 'Are you sure you want to delete the offline payment method?',
        'offlinePaymentMethodDeleted' => 'Offline Payment Method Deleted',
        'addPaymentMethod' => 'Add Payment Method',
        'razorpay' => 'Razorpay',
        'stripe' => 'Stripe',
        'flutterwave' => 'Flutterwave',
        'offline' => 'Offline Payment',
        'qr_code' => 'QR Code Payment',
        'paypal' => 'Paypal',
        'viewPaymentMethod' => 'View Payment Method',
        'offlinePaymentMethod' => 'Offline Payment Method',
        'restaurant' => 'Account Name',
        'paymentDate' => 'Payment Date',
        'packageDetails' => 'Package Details',
        'transactionId' => 'Transaction ID',
        'paymentGateway' => 'Payment Gateway',
        'choosePlan' => 'Choose Plan',
        'noPaymentOptionEnable' => 'No payment options are enabled.',
        'choosePaymentMethod' => 'Choose Payment Method',
        'amount' => 'Amount',
        'package' => 'Package',
        'paymentBy' => 'Payment By',
        'paymentDone' => 'Payment Done',
        'created' => 'Created',
        'status' => 'Status',
        'receipt' => 'Receipt',
        'paymentDate' => 'Payment Date',
        'payOnline' => 'Pay Online',
        'billingCycle' => 'Billing Cycle',
        'paybyQr' => 'Pay by QR Code',
        'accountDetails' => 'Account Details :',
        'showOtherPaymentOption' => 'Show Other Payment Option',
        'bankTransfer' => 'Bank Transfer',
        'payOffline' => 'Pay Offline',
        'purchaseHistory' => 'Purchase History',
        'offlineRequest' => 'Offline Request',
        'generalSettings' => 'General Settings',
        'planDetails' => 'Plan Details',
        'currentPlan' => 'Current Plan Name',
        'daysLeft' => ':count Days Left',
        'licenseExpireOn' => 'License Expire On',
        'noPlanAssigned' => 'No Plan Assigned',
        'expired' => 'Expired',
        'currentPlanType' => 'Current Plan Type',
        'offlineUploadFile' => 'Please attach a file of your offline payment receipt',
        'offlineDescriptionPlaceholder' => 'Provide details of your offline payment',
        'nextPaymentDate' => 'Next Payment Date',
        'remark' => 'Remark',
        'monthly' => 'Monthly',
        'annually' => 'Annually',
        'lifetimeAccess' => 'Lifetime Access',
        'billed' => 'Billed',
        'free' => 'Free',
        'billedTo' => 'Billed To',
        'paid' => 'Paid',
        'total' => 'Total',
        'paidVia' => 'Paid Via',
        'acceptOfflineRequest' => 'Accept Offline Request',
        'rejectOfflineRequest' => 'Reject Offline Request',
        'pickYourPlan' => 'Pick Your Plan',
        'noPaymentMethodSelected' => 'No payment method selected.',
        'cancelImmediately' => 'Cancel Immediately',
        'endOfBillingCycle' => 'End of Billing Cycle',
        'expiringToday' => 'Expiring Today',
        'paymentReceipt' => 'Payment Receipt',
        'trial' => 'Trial',
        'default' => 'Default',
        'standard' => 'Standard',
        'lifetime' => 'Lifetime',
        'free' => 'Free',
        'annual' => 'Annual',
        'payfast' => 'PayFast',
        'payfastLiveId' => 'Payfast Live ID',
        'payfastLiveKey' => 'Payfast Live KEY',
        'payfastLivePassphrase' => 'Payfast Live Passphrase',
        'payfastTestId' => 'Test Payfast ID',
        'payfastTestKey' => 'Test Payfast KEY',
        'payfastTestPassphrase' => 'Test Payfast Passphrase',
        'paypal' => 'Paypal',
        'paystack' => 'Paystack',
    ],

    'language' => [
        'languageCode' => 'Language Code',
        'languageName' => 'Language Name',
        'active' => 'Active',
        'rtl' => 'RTL',
        'addLanguage' => 'Add Language',
        'flagCode' => 'Flag Code',
        'flagCodeHelp' => 'Click here to get the flag code',
        'editLanguage' => 'Edit Language',
        'deleteLanguage' => 'Delete Language',
        'deleteLanguageMessage' => 'Are you sure you want to delete the language?',
        'defaultLanguage' => 'Default Language',
        'modifyDefaultLanguage' => 'To modify the default language, please navigate to the',
        'currentLanguageDefault' => 'The current language is set as the default.',
    ],

    'waiterRequest' => [
        'markCompleted' => 'Mark Attended',
        'doItLater' => 'Do it Later',
        'activeWaiterRequests' => 'Active Waiter Requests',
        'newWaiterRequests' => 'New Waiter Request',
        'newWaiterRequestForTable' => 'New Waiter Request for Table - :name',
        'activeWaiterRequestsCount' => 'Active Waiter Requests',
        'noWaiterRequest' => 'No waiter request found in this area.',
    ],

    'profile' => [
        'profileInfo' => 'Profile Information',
        'updateProfileInfo' => 'Update your account\'s profile information and email address.',
        'photo' => 'Photo',
        'selectNewPhoto' => 'Select A New Photo',
        'removePhoto' => 'Remove Photo',
        'name' => 'Name',
        'currentPassword' => 'Current Password',
        'newPassword' => 'New Password',
        'confirmPassword' => 'Confirm Password',
        'ensureAccountSecure' => 'Ensure your account is using a long, random password to stay secure.',
        'updatePassword' => 'Update Password',
        'twoFactorAuth' => 'Two Factor Authentication',
        'addSecurity' => 'Add additional security to your account using two factor authentication.',
        'finishEnablingTwoFactorAuth' => 'Finish enabling two factor authentication.',
        'notEnabledTwoFactorAuth' => 'You have not enabled two factor authentication.',
        'enabledTwoFactorAuth' => 'You have enabled two factor authentication.',
        'twoFactorAuthMessage' => 'When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone\'s Google Authenticator application.',
        'finishEnablingTwoFactorAuthMessage' => 'To finish enabling two factor authentication, scan the following QR code using your phone\'s authenticator application or enter the setup key and provide the generated OTP code.',
        'setupKey' => 'Setup Key',
        'recoveryCodesMessage' => 'Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.',
        'regenerateRecoveryCodes' => 'Regenerate Recovery Codes',
        'showRecoveryCodes' => 'Show Recovery Codes',
        'deleteAccount' => 'Delete Account',
        'permanentlyDeleteAccount' => 'Permanently delete your account.',
        'permanentlyDeleteAccountMessage' => 'Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.',
        'areYouSureDeleteAccount' => 'Are you sure you want to delete your account? Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.',
        'password' => 'Password',
        'browserSessions' => 'Browser Sessions',
        'browserSessionsDescription' => 'Manage and log out your active sessions on other browsers and devices.',
        'browserSessionsContent' => 'If necessary, you may log out of all of your other browser sessions across all of your devices. Some of your recent sessions are listed below; however, this list may not be exhaustive. If you feel your account has been compromised, you should also update your password.',
        'unknown' => 'Unknown',
        'thisDevice' => 'This Device',
        'lastActive' => 'Last Active',
        'logOutOtherBrowserSessions' => 'Log Out Other Browser Sessions',
        'done' => 'Done.',
        'confirmLogoutContent' => 'Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.',
        'passwordConfirmationContent' => 'For your security, please confirm your password to continue.',
        'confirm' => 'Confirm',
        'confirmPassword' => 'Confirm Password',
        'code' => 'Code',
        'recoveryCode' => 'Recovery Code',
        'useRecoveryCode' => 'Use a recovery code',
        'useAuthCode' => 'Use an authentication code',
        'login' => 'Log In',
    ],

    'expenses' => [
        'title' => 'Expenses',
        'amount' => 'Amount',
        'category' => 'Category',
        'vendor' => 'Vendor',
        'expenseDate' => 'Expense Date',
        'paymentDate' => 'Payment Date',
        'paymentDueDate' => 'Due Date',
        'paymentMethod' => 'Payment Method',
        'paymentStatus' => 'Payment Status',
        'description' => 'Description',
        'receipt' => 'Receipt',
        'addExpense' => 'Add Expense',
        'editExpense' => 'Edit Expense',
        'deleteExpense' => 'Delete Expense',
        'selectCategory' => 'Select Category',
        'selectVendor' => 'Select Vendor',
        'pending' => 'Pending',
        'paid' => 'Paid',
        'addCategory' => 'Add Category',
        'addVendor' => 'Add Vendor',
        'expenses' => 'Expenses',
        'filterExpenseCategory' => 'Filter by Expense Category',
        'filterPaymentMethod' => 'Filter by Payment Method',
        'expensesCategory' => 'Expenses Category',
        'expensesPaymentMethod' => 'Payment Method',
        'filterVendor' => 'Filter by Vendor',
        'expensesVendor' => 'Vendor',
        'filterDateRange' => 'Filter by Date Range',
        'expensesDateRange' => 'Date Range',
        'startDate' => 'Start Date',
        'endDate' => 'End Date',
        'deleteExpensesMessage' => 'Are you sure you want to delete the expense?',
        'editExpense' => 'Edit Expense',
        'total_amount_spent' => 'Total Amount Spent',
        'no_of_transaction' => 'Number of Transaction',
        'total_expense' => 'Total Expense',
        'percentage_of_total' => 'Percentage of Total',
        'addExpenseCategory' => 'Add Expense Category',
        'receiptPreview' => 'Receipt Preview',
        'expenseDetails' => 'Expense Details',
        'expenseTitle' => 'Expense Title',
        'expenseCategory' => 'Expense Category',
        'expenseAmount' => 'Expense Amount',
        'expenseDate' => 'Expense Date',
        'expenseDescription' => 'Expense Description',
        'totalPaymentDue' => 'Total Payment Due',
        'lastDueDate' => 'Last Due Date',
        'paymentStatus' => 'Payment Status',

        'status' => [
            'paid' => 'Paid',
            'pending' => 'Pending',
            'approved' => 'Approved',
            'rejected' => 'Rejected'
        ],
        'methods' => [
            'cash' => 'Cash',
            'bank_transfer' => 'Bank Transfer',
            'credit_card' => 'Credit Card',
            'debit_card' => 'Debit Card',
            'check' => 'Cheque',
            'digital_wallet' => 'Digital Wallet',
        ],
        'reports' => [
            'outstandingPaymentReport' => 'Outstanding Payment Report',
            'vendorWiseExpensePaymentReport' => 'Vendor Wise Expense Payment Report',
            'expenseSummaryReport' => 'Expense Summary Report',
            'NoReportsFound' => 'No reports Found',
            'description' => 'Description',
        ],
        'noReceiptAvailable' => 'No receipt available.',
        'editExpenseCategory' => 'Edit Expense Category',
    ],

    'vendors' => [
        'vendorName' => 'Vendor Name',
        'vendors' => 'Vendors',
        'addVendor' => 'Add Vendor',
        'editVendor' => 'Edit Vendor',
        'deleteVendor' => 'Delete Vendor',
        'addVendor' => 'Add Vendor',
        'id' => 'Id',
        'name' => 'Name',
        'email' => 'Email',
        'phone' => 'Phone',
        'address' => 'Address',
        'contactPerson' => 'Contact Person',
        'actions' => 'Actions',
        'deleteVendorMessage' => 'Are you sure you want to delete Vendor ?',

    ],


    'report' => [
        'totalSales' => 'Total Sales',
        'orders' => 'Orders',
        'cashPayments' => 'Cash Payments',
        'digitalPayments' => 'Digital Payments',
        'averageOrderValue' => 'Average Order Value',
        'totalRevenue' => 'Total Revenue',
        'imSales' => 'Item Sales',
        'quantitySold' => 'Quantity Sold',
        'topSellingItems' => 'Top Selling Items',
        'sellingPrice' => 'Selling Price',
        'salesByCategory' => 'Sales By Category',
        'salesByPaymentMethod' => 'Sales By Payment Method',
        'orderType' => 'Order Type',
        'salesByTime' => 'Sales By Time',
        'salesByDay' => 'Sales By Day',
        'salesByMonth' => 'Sales By Month',
        'salesByYear' => 'Sales By Year',
        'totalTaxes' => 'Total Taxes',
        'totalDiscounts' => 'Total Discounts',
        'averageItems' => 'Average Items Per Order',
        'grossSales' => 'Gross Sales',
        'netSales' => 'Net Sales',
        'totalRefunds' => 'Total Refunds',
        'pendingPayments' => 'Pending Payments',
        'canceledOrders' => 'Canceled Orders',
        'totalOrders' => 'Total Orders',
        'totalQuantitySold' => 'Total Quantity Sold',
        'sumOfTotalRevenue' => 'Sum of Total Revenue',
        'totalCharges' => 'Total Charges',
        'itemReportMessage' => 'View detailed sales and performance of items',
        'salesReportMessage' => 'Check and track your restaurant\'s earnings',
        'categoryReportMessage' => 'See sales by category to understand performance',
        'paymentMethods' => 'Payment Methods',
    ],


    'printerSetting' => [
     'browserPopupPrint' => 'Browser Popup Print',
     'directPrint' => 'Direct Print',
     'title' => 'Title (To identify printer easily)',
     'printChoice' => 'Printing Choice',
     'printFormat' => 'Print Format',
     'noAutoKotPrint' => 'No Auto KOT Print',
     'thermal56mm' => 'Thermal 56mm',
     'thermal80mm' => 'Thermal 80mm',
     'invoiceQrCode' => 'Invoice QR Code',
     'disable' => 'Disable',
     'regularQrCode' => 'Regular QR Code',
     'ZATCAQrCode' => 'ZATCA QR Code',
     'printerType' => 'Printer Type',
     'networkPrinter' => 'Network Printer',
     'usbPrinter' => 'USB Printer',
     'charactersPerLine' => 'Characters Per Line',
     'printerIPAddress' => 'Printer IP Address',
     'printerPortAddress' => 'Printer Port Address',
     'shareName' => 'Printer Name',
     'openCashDrawerWhenPrintingInvoice' => 'Open Cash Drawer When Printing Invoice',
     'yes' => 'Yes',
     'no' => 'No',
     'ipv4Address' => 'IPv4 Address',
     'thermalPrinter' => 'Thermal Printer',
     'nonThermalPrinter' => 'Non Thermal Printer',
     'name' => 'Printer Name',
     'printingChoice' => 'Printing Choice',
     'printFormat' => 'Print Format',
     'ipAddress' => 'IP Address',
     'port' => 'Port',
     'action' => 'Actions',
     'deletePritnerConfirm' => 'Are you sure you want to delete this printer?',
     'deletePrinter' => 'Delete Printer',
    'isThermal' => 'Is Thermal',
    'printer_offline' => 'Printer is offline',
    'printer_online' => 'Printer is online',
    'printer_not_found' => 'Printer not found',
    'kotSelection' => 'Select Kitchen',
    'orderSelection' => 'Select Pos Terminal',
    'selectedKitchens' => 'Selected Kitchens',
    'selectedPosTerminal' => 'Selected Pos Terminal',
    'addPrinter' => 'Add Printer',
    'isDefault' => 'Is Default',
    'traditionalPayments' => 'Traditional Payments',
    'paymentGateways' => 'Payment Gateways',
    'additionalAmounts' => 'Additional Amounts',
    'network' => 'Network',
    'windows' => 'Windows',
    'kitchens' => 'Kitchens',
    'orders' => 'Orders',
    'default' => 'Default',
    'select' => 'Select',
    'isActive' => 'Is Active',
    'thermal112mm' => 'Thermal 112mm',
    'traditionalPayments' => 'Traditional Payments',
    'paymentGateways' => 'Payment Gateways',
    'additionalAmounts' => 'Additional Amounts',
    'salesDataFor' => 'Data for',
    'timePeriod' => 'Time period:',
    'salesDataFrom' => 'Data from',
    'timePeriodEachDay' => 'Time period each day:',
    'connected' => 'Connected',
    'disconnected' => 'Disconnected',

    ],

    'moduleLicenseStatus' => [
        'moduleLicenseStatus' => 'Module License Status',
        'licensed' => 'Licensed',
        'verificationRequired' => 'Verification Required',
        'notActivated' => 'Not Activated',
        'supportExpired' => 'Support Expired',
        'supportUntil' => 'Support Until',
        'supportExpiredMessage' => 'Support expired on',
        'supportUntilMessage' => 'Support until',
    ],

];
