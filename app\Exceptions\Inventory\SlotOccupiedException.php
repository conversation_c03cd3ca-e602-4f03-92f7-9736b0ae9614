<?php

namespace App\Exceptions\Inventory;

use Exception;

class SlotOccupiedException extends Exception
{
    protected $slotId;
    protected $currentMenuItemId;
    protected $attemptedMenuItemId;

    public function __construct(
        int $slotId,
        int $currentMenuItemId,
        int $attemptedMenuItemId,
        string $message = null
    ) {
        $this->slotId = $slotId;
        $this->currentMenuItemId = $currentMenuItemId;
        $this->attemptedMenuItemId = $attemptedMenuItemId;

        $message = $message ?: "Slot {$slotId} is already occupied by menu item {$currentMenuItemId}. Cannot allocate menu item {$attemptedMenuItemId}.";
        
        parent::__construct($message);
    }

    public function getSlotId(): int
    {
        return $this->slotId;
    }

    public function getCurrentMenuItemId(): int
    {
        return $this->currentMenuItemId;
    }

    public function getAttemptedMenuItemId(): int
    {
        return $this->attemptedMenuItemId;
    }

    public function getUserFriendlyMessage(): string
    {
        return "This slot is already occupied by another menu item. Please choose an empty slot or remove the current allocation first.";
    }
}