<?php

namespace App\Traits;

use App\Services\OrderInventoryService;
use App\Models\Order;
use App\Models\Branch;
use Illuminate\Support\Facades\App;

trait HandlesOrderInventory
{
    protected ?OrderInventoryService $orderInventoryService = null;

    /**
     * Get the order inventory service instance
     */
    protected function getOrderInventoryService(): OrderInventoryService
    {
        if (!$this->orderInventoryService) {
            $this->orderInventoryService = App::make(OrderInventoryService::class);
        }

        return $this->orderInventoryService;
    }

    /**
     * Check if inventory is available for order items
     */
    protected function checkOrderInventoryAvailability(Branch $branch, array $orderItems): array
    {
        return $this->getOrderInventoryService()->checkInventoryAvailability($branch, $orderItems);
    }

    /**
     * Validate order inventory before processing
     */
    protected function validateOrderInventory(array $orderItems, Branch $branch): array
    {
        return $this->getOrderInventoryService()->validateOrderInventory($orderItems, $branch);
    }

    /**
     * Reserve inventory for an order
     */
    protected function reserveOrderInventory(Order $order): bool
    {
        try {
            return $this->getOrderInventoryService()->reserveInventoryForOrder($order);
        } catch (\Exception $e) {
            // Log the error and return false
            \Log::error('Failed to reserve inventory for order: ' . $e->getMessage(), [
                'order_id' => $order->id,
                'order_number' => $order->order_number
            ]);
            return false;
        }
    }

    /**
     * Restore inventory for a cancelled order
     */
    protected function restoreOrderInventory(Order $order): bool
    {
        try {
            return $this->getOrderInventoryService()->restoreInventoryForOrder($order);
        } catch (\Exception $e) {
            // Log the error and return false
            \Log::error('Failed to restore inventory for order: ' . $e->getMessage(), [
                'order_id' => $order->id,
                'order_number' => $order->order_number
            ]);
            return false;
        }
    }

    /**
     * Get inventory impact summary for an order
     */
    protected function getOrderInventoryImpact(Order $order): array
    {
        return $this->getOrderInventoryService()->getOrderInventoryImpact($order);
    }

    /**
     * Get low inventory alerts for a branch
     */
    protected function getLowInventoryAlerts(Branch $branch, int $threshold = 5): \Illuminate\Support\Collection
    {
        return $this->getOrderInventoryService()->getLowInventoryAlerts($branch, $threshold);
    }

    /**
     * Get out of stock items for a branch
     */
    protected function getOutOfStockItems(Branch $branch): \Illuminate\Support\Collection
    {
        return $this->getOrderInventoryService()->getOutOfStockItems($branch);
    }

    /**
     * Format order items for inventory checking
     */
    protected function formatOrderItemsForInventoryCheck(array $orderItemList, array $orderItemQty): array
    {
        $formattedItems = [];

        foreach ($orderItemList as $key => $menuItem) {
            $formattedItems[] = [
                'menu_item_id' => $menuItem->id,
                'quantity' => $orderItemQty[$key] ?? 1
            ];
        }

        return $formattedItems;
    }

    /**
     * Handle inventory validation errors
     */
    protected function handleInventoryValidationErrors(array $validation): void
    {
        if (!$validation['valid']) {
            $errorMessages = [];
            
            foreach ($validation['errors'] as $error) {
                $errorMessages[] = "Insufficient inventory for menu item ID {$error['menu_item_id']}: " .
                    "Requested {$error['requested_quantity']}, Available {$error['available_quantity']}";
            }

            throw new \Exception('Inventory validation failed: ' . implode('; ', $errorMessages));
        }
    }

    /**
     * Show inventory warnings to user
     */
    protected function showInventoryWarnings(array $validation): void
    {
        if (!empty($validation['warnings'])) {
            foreach ($validation['warnings'] as $warning) {
                $this->alert('warning', 
                    "Low inventory warning for menu item ID {$warning['menu_item_id']}: " .
                    "Only {$warning['available_quantity']} units available",
                    ['toast' => true]
                );
            }
        }
    }
}