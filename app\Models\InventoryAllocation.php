<?php

namespace App\Models;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class InventoryAllocation extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'slot_id',
        'menu_item_id',
        'current_quantity'
    ];

    protected $casts = [
        'current_quantity' => 'integer',
    ];

    /**
     * Validation rules for inventory allocation creation and updates
     */
    public static function validationRules($allocationId = null): array
    {
        return [
            'slot_id' => [
                'required',
                'exists:slots,id',
                $allocationId ? '' : 'unique:inventory_allocations,slot_id'
            ],
            'menu_item_id' => 'required|exists:menu_items,id',
            'current_quantity' => 'required|integer|min:0'
        ];
    }

    /**
     * Get validation messages
     */
    public static function validationMessages(): array
    {
        return [
            'slot_id.required' => 'Slot is required.',
            'slot_id.exists' => 'Selected slot does not exist.',
            'slot_id.unique' => 'This slot is already occupied by another menu item.',
            'menu_item_id.required' => 'Menu item is required.',
            'menu_item_id.exists' => 'Selected menu item does not exist.',
            'current_quantity.required' => 'Current quantity is required.',
            'current_quantity.integer' => 'Current quantity must be an integer.',
            'current_quantity.min' => 'Current quantity cannot be negative.'
        ];
    }

    /**
     * Get the slot that this allocation belongs to
     */
    public function slot(): BelongsTo
    {
        return $this->belongsTo(Slot::class);
    }

    /**
     * Get the menu item that is allocated to this slot
     */
    public function menuItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class);
    }

    /**
     * Check if we can add the specified quantity without exceeding slot capacity
     */
    public function canAddQuantity(int $quantity): bool
    {
        if ($quantity <= 0) {
            return false;
        }

        $slot = $this->slot;
        if (!$slot) {
            return false;
        }

        return ($this->current_quantity + $quantity) <= $slot->capacity;
    }

    /**
     * Check if we can remove the specified quantity without going below zero
     */
    public function canRemoveQuantity(int $quantity): bool
    {
        if ($quantity <= 0) {
            return false;
        }

        return ($this->current_quantity - $quantity) >= 0;
    }

    /**
     * Add inventory quantity to this allocation
     */
    public function addInventory(int $quantity): bool
    {
        if (!$this->canAddQuantity($quantity)) {
            return false;
        }

        $previousQuantity = $this->current_quantity;
        $this->current_quantity += $quantity;
        $result = $this->save();

        if ($result) {
            event(new \App\Events\Inventory\InventoryUpdated(
                $this,
                'added',
                $quantity,
                $previousQuantity
            ));
        }

        return $result;
    }

    /**
     * Remove inventory quantity from this allocation
     */
    public function removeInventory(int $quantity): bool
    {
        if (!$this->canRemoveQuantity($quantity)) {
            return false;
        }

        $previousQuantity = $this->current_quantity;
        $this->current_quantity -= $quantity;
        $result = $this->save();

        if ($result) {
            event(new \App\Events\Inventory\InventoryUpdated(
                $this,
                'removed',
                $quantity,
                $previousQuantity
            ));
        }

        return $result;
    }

    /**
     * Set the inventory quantity (with capacity validation)
     */
    public function setQuantity(int $quantity): bool
    {
        if ($quantity < 0) {
            return false;
        }

        $slot = $this->slot;
        if (!$slot || $quantity > $slot->capacity) {
            return false;
        }

        $previousQuantity = $this->current_quantity;
        $quantityChanged = abs($quantity - $previousQuantity);
        $this->current_quantity = $quantity;
        $result = $this->save();

        if ($result && $quantityChanged > 0) {
            event(new \App\Events\Inventory\InventoryUpdated(
                $this,
                'set',
                $quantityChanged,
                $previousQuantity
            ));
        }

        return $result;
    }

    /**
     * Get remaining capacity in the slot
     */
    public function getRemainingCapacity(): int
    {
        $slot = $this->slot;
        if (!$slot) {
            return 0;
        }

        return $slot->capacity - $this->current_quantity;
    }

    /**
     * Get capacity utilization percentage
     */
    public function getCapacityUtilization(): float
    {
        $slot = $this->slot;
        if (!$slot || $slot->capacity === 0) {
            return 0.0;
        }

        return ($this->current_quantity / $slot->capacity) * 100;
    }

    /**
     * Check if allocation is at capacity
     */
    public function isAtCapacity(): bool
    {
        $slot = $this->slot;
        if (!$slot) {
            return false;
        }

        return $this->current_quantity >= $slot->capacity;
    }

    /**
     * Check if allocation is near capacity (80% or more)
     */
    public function isNearCapacity(): bool
    {
        return $this->getCapacityUtilization() >= 80.0;
    }

    /**
     * Check if allocation is empty
     */
    public function isEmpty(): bool
    {
        return $this->current_quantity === 0;
    }
}