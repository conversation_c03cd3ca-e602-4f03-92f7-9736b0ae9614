<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Tray;
use App\Models\Slot;

class SeedTraySlots extends Command
{
    protected $signature = 'seed:trayslots
        {--b= : Branch ID}
        {--t= : Number of trays}
        {--s=* : Slot settings (e.g. a=10, 5 for default)}
        {--c=* : Capacity settings (e.g. a1=5, a*=10, 5 for default)}';

    protected $description = 'Seed trays and slots for a branch with flexible tray and slot options';

    public function handle()
    {
        $branchId = $this->option('b');
        $trayCount = (int) $this->option('t');
        $slotOptions = $this->option('s') ?? [];
        $capacityOptions = $this->option('c') ?? [];

        if (!$branchId || $trayCount < 1 || $trayCount > 26) {
            $this->error("Invalid branch ID or tray count (must be 1–26).");
            return;
        }

        $alphabet = range('a', 'z');

        for ($i = 0; $i < $trayCount; $i++) {
            $trayLetter = $alphabet[$i];
            $trayName = strtoupper($trayLetter);

            $tray = Tray::create([
                'branch_id'  => $branchId,
                'name'       => $trayName,
                'sort_order' => $i + 1,
            ]);

            $slotCount = $this->resolveSlotCount($trayLetter, $slotOptions);

            for ($j = 0; $j < $slotCount; $j++) {
                $slotNumber = $j + 1;
                $capacity = $this->resolveCapacity($trayLetter, $slotNumber, $capacityOptions);

                Slot::create([
                    'tray_id'    => $tray->id,
                    'name'       => (string) $slotNumber,
                    'capacity'   => $capacity,
                    'sort_order' => $slotNumber,
                ]);
            }

            $this->info("Tray {$trayName} created with {$slotCount} slots.");
        }

        $this->info("✅ Seeding complete for branch ID {$branchId}.");
    }

    private function resolveSlotCount(string $trayLetter, array $options): int
    {
        foreach ($options as $option) {
            if (preg_match("/^{$trayLetter}=(\d+)$/", $option, $m)) {
                return (int) $m[1];
            }
        }

        foreach ($options as $option) {
            if (preg_match("/^\d+$/", $option)) {
                return (int) $option; // default for unspecified trays
            }
        }

        return 1;
    }

    private function resolveCapacity(string $trayLetter, int $slotNumber, array $options): int
    {
        $specificKey = "{$trayLetter}{$slotNumber}";
        $trayAllKey  = "{$trayLetter}*";

        foreach ($options as $option) {
            if (preg_match("/^{$specificKey}=(\d+)$/", $option, $m)) {
                return (int) $m[1];
            }
        }

        foreach ($options as $option) {
            if (preg_match("/^{$trayAllKey}=(\d+)$/", $option, $m)) {
                return (int) $m[1];
            }
        }

        foreach ($options as $option) {
            if (preg_match("/^\d+$/", $option)) {
                return (int) $option; // default for all slots
            }
        }

        return 1;
    }
}

