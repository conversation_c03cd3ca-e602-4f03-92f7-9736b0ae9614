<?php

namespace App\Exceptions\Inventory;

use Exception;

class InvalidAllocationException extends Exception
{
    protected $menuItemId;
    protected $violationType;
    protected $details;

    public const VIOLATION_NO_SLOTS = 'no_slots';
    public const VIOLATION_INVALID_QUANTITY = 'invalid_quantity';
    public const VIOLATION_MENU_ITEM_NOT_FOUND = 'menu_item_not_found';
    public const VIOLATION_SLOT_NOT_FOUND = 'slot_not_found';
    public const VIOLATION_BRANCH_MISMATCH = 'branch_mismatch';

    public function __construct(
        string $violationType,
        array $details = [],
        string $message = null
    ) {
        $this->violationType = $violationType;
        $this->details = $details;
        $this->menuItemId = $details['menu_item_id'] ?? null;

        $message = $message ?: $this->generateMessage();
        
        parent::__construct($message);
    }

    public function getViolationType(): string
    {
        return $this->violationType;
    }

    public function getDetails(): array
    {
        return $this->details;
    }

    public function getMenuItemId(): ?int
    {
        return $this->menuItemId;
    }

    private function generateMessage(): string
    {
        switch ($this->violationType) {
            case self::VIOLATION_NO_SLOTS:
                return "Menu item {$this->menuItemId} must be allocated to at least one slot.";
            case self::VIOLATION_INVALID_QUANTITY:
                return "Invalid quantity specified. Quantity must be a positive integer.";
            case self::VIOLATION_MENU_ITEM_NOT_FOUND:
                return "Menu item {$this->menuItemId} not found or not accessible.";
            case self::VIOLATION_SLOT_NOT_FOUND:
                return "Slot {$this->details['slot_id']} not found or not accessible.";
            case self::VIOLATION_BRANCH_MISMATCH:
                return "Menu item and slot must belong to the same branch.";
            default:
                return "Invalid allocation operation.";
        }
    }

    public function getUserFriendlyMessage(): string
    {
        switch ($this->violationType) {
            case self::VIOLATION_NO_SLOTS:
                return "Each menu item must be assigned to at least one slot.";
            case self::VIOLATION_INVALID_QUANTITY:
                return "Please enter a valid quantity (positive number).";
            case self::VIOLATION_MENU_ITEM_NOT_FOUND:
                return "The selected menu item is not available.";
            case self::VIOLATION_SLOT_NOT_FOUND:
                return "The selected slot is not available.";
            case self::VIOLATION_BRANCH_MISMATCH:
                return "Menu items can only be allocated to slots within the same branch.";
            default:
                return "Unable to complete the allocation. Please check your input and try again.";
        }
    }
}