<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class InventoryAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission = null): Response
    {
        $user = auth()->user();

        // Check if user is authenticated
        if (!$user) {
            abort(401, 'Unauthorized');
        }

        // Check if user has a branch assigned
        if (!$user->branch) {
            abort(403, 'No branch assigned to user');
        }

        // Check if user has the required permission
        if ($permission && !$user->can($permission)) {
            abort(403, 'Insufficient permissions for inventory management');
        }

        // Ensure user can only access their own branch's inventory
        $request->merge(['branch_id' => $user->branch_id]);

        return $next($request);
    }
}