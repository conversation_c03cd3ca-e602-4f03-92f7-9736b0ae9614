<?php

namespace App\Http\Controllers;
use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\ItemCategory;
use App\Models\ModifierGroup;
use App\Models\ModifierOption;
use App\Models\User;
use App\Models\Role;
use App\Models\Branch;
use App\Models\Country;
use App\Models\Restaurant;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Permission;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class LlmMenuController extends Controller
{
    protected string $domain = '@gebeya.top';    
    
    public function seed(Request $request)
    {
        $data = $request->json()->all();

        $validator = Validator::make($data, [
            'branch_id' => 'required|integer|exists:branches,id',
            'menus' => 'required|array',
            'menus.*.menu_name' => 'required|string',
            'menus.*.categories' => 'required|array',
            'menus.*.categories.*.category_name' => 'required|string',
            'menus.*.categories.*.items' => 'required|array',
            'menus.*.categories.*.items.*.item_name' => 'required|string|max:255',
            'menus.*.categories.*.items.*.price' => 'required|numeric',
            'menus.*.categories.*.items.*.type' => 'nullable|string|in:veg,non-veg,nonveg',
            'menus.*.categories.*.items.*.description' => 'nullable|string',
            'menus.*.categories.*.items.*.image' => 'nullable|string',
            'menus.*.categories.*.items.*.preparation_time' => 'nullable|integer',
            'menus.*.categories.*.items.*.modifiers' => 'nullable|array',
            'menus.*.categories.*.items.*.modifiers.*.name' => 'sometimes|required|string',
            'menus.*.categories.*.items.*.modifiers.*.options' => 'nullable|array',
            'menus.*.categories.*.items.*.modifiers.*.options.*.name' => 'sometimes|required|string',
            'menus.*.categories.*.items.*.modifiers.*.options.*.price' => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $branch = Branch::findOrFail($data['branch_id']);
            $menuData = ['menus' => $data['menus']];

            $this->addMenu($branch, $menuData);

            return response()->json(['message' => 'Menu data seeded successfully.']);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Branch not found.'], 404);
        } catch (\Exception $e) {
            Log::error('LLM Menu Seeding failed: ' . $e->getMessage());
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    public function seedFullRestaurant(Request $request)
    {
        $data = $request->json()->all();

        $validator = Validator::make($data, [
            'name' => 'required|string|max:255',
            'address' => 'nullable|string',
            'phone_number' => 'nullable|string',
            'facebook_link' => 'nullable|string|url',
            'instagram_link' => 'nullable|string|url',
            'twitter_link' => 'nullable|string|url',
            'timezone' => 'nullable|string',
            'email' => 'nullable|email',
            'country_id' => 'nullable|integer',
            'package_id' => 'nullable|integer',
            'package_type' => 'nullable|string',
	    'about_us' => 'nullable|string',
	    'theme_hex' => 'nullable|string',
            'theme_rgb' => 'nullable|string',
            'branch_name' => 'nullable|string',
            'branch_address' => 'nullable|string',
            'menus' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $companyData = $data;
            $menuData = ['menus' => $data['menus'] ?? []];

            $this->addResto($companyData, $menuData);

            return response()->json(['message' => 'Restaurant, branch, and menu seeded successfully.']);
        } catch (\Exception $e) {
            Log::error('Restaurant seeding failed: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to seed restaurant and menu.'], 500);
        }
    }

    public function addResto($companyData): void
    {
        if (empty($companyData['name'])) {
            Log::warning('RestaurantSeeder: Company name is required.');
            return;
        }

        // Create Restaurant
        $companyName = trim($companyData['name']);
        $resto = new Restaurant();
        $resto->name = $companyName;
        $resto->address = $companyData['address'] ?? '';
        $resto->phone_number = $companyData['phone_number'] ?? '';
        $resto->facebook_link = $companyData['facebook_link'] ?? '';
        $resto->instagram_link = $companyData['instagram_link'] ?? '';
        $resto->twitter_link = $companyData['twitter_link'] ?? '';

        $resto->timezone = $companyData['timezone'] ?? 'Africa/Addis_Ababa';
        $resto->email = $companyData['email'] ?? (Str::slug($companyName, '.') . $this->domain);
        $resto->country_id = $companyData['country_id'] ?? 1;
        $resto->package_id = $companyData['package_id'] ?? 1;
        $resto->package_type = $companyData['package_type'] ?? 'annual';
        $resto->about_us = $companyData['about_us'] ?? Restaurant::ABOUT_US_DEFAULT_TEXT;
        $resto->theme_hex = $companyData['theme_hex'] ?? '#000000';
        $resto->theme_rgb = $companyData['theme_rgb'] ?? '0, 0, 0';

        $resto->save();
        Log::info("Created restaurant: {$companyName}");

        // Create Branch
        $branch = new Branch();
        $branch->restaurant_id = $resto->id;
        $branch->name = $companyData['branch_name'] ?? 'Main Branch';
        $branch->address = $companyData['branch_address'] ?? '';
        $branch->save();
        Log::info("Created branch for restaurant: {$companyName}");

        // send to the menu seeder the whole company data
        $this->addMenu($branch->id , $companyData);
        $this->addusers($branch , 1, 2); # 1 admin 2 waiters

        $branch->generateQrCode();
        $this->addKotPlaces($branch);
        $branch->generateKotSetting();

        Log::info("Finished seeding for: {$companyName}");
     }

     public function addusers($branch, $numAdmins = 1, $numWaiters = 1)
     {
        $adminRole = Role::create(['name' => 'Admin_'.$branch->restaurant->id, 'display_name' => 'Admin', 'guard_name' => 'web', 'restaurant_id' => $branch->restaurant->id   ]);
        $branchHeadRole = Role::create(['name' => 'Branch Head_'.$branch->restaurant->id, 'display_name' => 'Branch Head', 'guard_name' => 'web', 'restaurant_id' => $branch->restaurant->id]);
        $waiterRole = Role::create(['name' => 'Waiter_'.$branch->restaurant->id, 'display_name' => 'Waiter', 'guard_name' => 'web', 'restaurant_id' => $branch->restaurant->id]);
        $chefRole = Role::create(['name' => 'Chef_'.$branch->restaurant->id, 'display_name' => 'Chef', 'guard_name' => 'web', 'restaurant_id' => $branch->restaurant->id]);

        $allPermissions = Permission::get()->pluck('name')->toArray();
        $adminRole->syncPermissions($allPermissions);
        $branchHeadRole->syncPermissions($allPermissions);

         $restoName = Str::slug($branch->restaurant->name, '.');

         for ($i = 0; $i < $numAdmins; $i++) {
             $admin = User::create([
                 'name' =>  $restoName . 'admin' . $i,
                 'email' => $restoName . 'admin' . $i . $this->domain,
                 'password' => bcrypt('addis123'),
                 'restaurant_id' => $branch->restaurant->id
             ]);

             $adminRole = Role::where('name', 'Admin_' . $branch->restaurant_id)->first();
             $admin->assignRole($adminRole);
         }

         // Create Waiters
         for ($i = 0; $i < $numWaiters; $i++) {
             $waiter = User::create([
                 'name' =>  'waiter' . $i,
                 'email' => 'waiter' . $i . '@' . $restoName,
                 'password' => bcrypt('12345'),
                 'restaurant_id' => $branch->restaurant->id,
                 'branch_id' => $branch->id
             ]);

             $waiterRole = Role::where('name', 'Waiter_' . $branch->restaurant_id)->first();
             $waiter->assignRole($waiterRole);
         }
     }

    public function addKotPlaces($branch)
    {
        if (!$branch) {
            $this->command->warn(__('messages.noBranchFound'));
            return;
        }

        // Create default KOT place
        $kotPlace = $branch->kotPlaces()->create([
            'name' => 'Default Kitchen',
            'branch_id' => $branch->id,
            'printer_id' => null, // Will update after printer is created
            'type' => 'food',
            'is_active' => true,
            'is_default' => true,
        ]);

        // Create default order place
        $orderPlace = $branch->orderPlaces()->create([
            'name' => 'Default POS Terminal',
            'branch_id' => $branch->id,
            'printer_id' => null, // Will update after printer is created
            'type' => 'vegetarian',
            'is_active' => true,
            'is_default' => true,
        ]);

        // Create printer and assign KOT and Order place IDs
        $printer = $branch->printerSettings()->create([
            'name' => 'Default Thermal Printer',
            'restaurant_id' => $branch->restaurant_id,
            'branch_id' => $branch->id,
            'is_active' => true,
            'is_default' => true,
            'printing_choice' => 'browserPopupPrint',
            'kots' => json_encode([$kotPlace->id]),
            'orders' => json_encode([$orderPlace->id]),
            'type' => null,
            'char_per_line' => null,
            'print_format' => null,
            'invoice_qr_code' => null,
            'open_cash_drawer' => null,
            'ipv4_address' => null,
            'thermal_or_nonthermal' => null,
            'share_name' => null,
        ]);

        // Update KOT and Order place with printer_id
        $kotPlace->printer_id = $printer->id;
        $kotPlace->save();

        $orderPlace->printer_id = $printer->id;
        $orderPlace->save();
    }

    public function addMenu($branchId, array $menuData): void
    {


        if (empty($menuData) || !isset($menuData['menus'])) {
            Log::error("MenuSeeder called but No menu data provided or data is malformed" . json_encode($menuData));
            return;
        }
        Log::info("adding menu to branch {$branchId}");
        foreach (($menuData['menus'] ?? []) as $menu) {
            if (empty($menu['menu_name'])) continue;
            $menuName = strtolower(trim($menu['menu_name']));

            $menuModel = Menu::whereJsonContains('menu_name->en', $menuName)->where('branch_id', $branchId)->first();
            if (!$menuModel) {
                $menuModel = Menu::create(['menu_name' => $menuName,'branch_id' => $branchId ] , [] );
                Log::info("Created new menu: {$menuName}");
            }

            foreach (($menu['categories'] ?? []) as $category) {
                if (empty($category['category_name'])) continue;

                $categoryName = strtolower(trim($category['category_name']));
                $categoryModel = ItemCategory::whereJsonContains('category_name->en', $categoryName)->where('branch_id', $branchId)->first();

                if (!$categoryModel) {
                    $categoryModel = ItemCategory::create(['category_name' => $categoryName,'branch_id' => $branchId ], [] );
                    Log::info("Created new catagory: {$categoryName}");
                }

                foreach (($category['items'] ?? []) as $item) {
                    if (empty($item['item_name']) || !isset($item['price'])) continue;

                    $menuItem = MenuItem::updateOrCreate(
                        [
                            'item_name' => trim($item['item_name']),
                            'branch_id' => $branchId,
                            'menu_id' => $menuModel->id,
                        ],
                        [
                            'item_category_id' => $categoryModel->id,
                            'type' => $this->getItemType($item['type'] ?? 'veg'),
                            'price' => $item['price'],
                            'description' => $item['description'] ?? null,
                            'image' => $item['image'] ?? null,
                            'preparation_time' => $item['preparation_time'] ?? 20,
                        ]
                    );

                    if (!empty($item['modifiers'])) {
                        foreach ($item['modifiers'] as $modifierGroupData) {
                            if (empty($modifierGroupData['name'])) continue;

                            $modifierGroup = ModifierGroup::updateOrCreate(
                                ['name' => strtolower(trim($modifierGroupData['name'])), 'branch_id' => $branchId]
                            );

                            if (!empty($modifierGroupData['options'])) {
                                foreach ($modifierGroupData['options'] as $optionData) {
                                    if (empty($optionData['name'])) continue;

                                    ModifierOption::updateOrCreate(
                                        [
                                            'name' => trim($optionData['name']),
                                            'modifier_group_id' => $modifierGroup->id,
                                        ],
                                        [
                                            'price' => $optionData['price'] ?? 0,
                                            'is_available' => 1,
                                        ]
                                    );
                                }
                            }

                            $menuItem->modifierGroups()->syncWithoutDetaching([$modifierGroup->id]);
                        }
                    }
                }
            }
        }
    }

    /**
     * Get item type from string.
     *
     * @param string $type
     * @return int
     */
    private function getItemType(string $type): int
    {
        // We are using 1 for VEG, 2 for NONVEG as an example.
        return match (strtolower($type)) {
            'veg' => 1,
            'non-veg', 'nonveg' => 2,
            default => 1, // Default to VEG if unknown
        };
    }

}
