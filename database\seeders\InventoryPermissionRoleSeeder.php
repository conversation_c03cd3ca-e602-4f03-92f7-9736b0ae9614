<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Module;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class InventoryPermissionRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the Inventory module
        $inventoryModule = Module::where('name', 'Inventory')->first();
        
        if (!$inventoryModule) {
            $this->command->error('Inventory module not found. Please run migrations first.');
            return;
        }

        // Get all inventory permissions
        $inventoryPermissions = Permission::where('module_id', $inventoryModule->id)->pluck('name')->toArray();
        
        if (empty($inventoryPermissions)) {
            $this->command->error('No inventory permissions found. Please run migrations first.');
            return;
        }

        // Define role permissions mapping
        $rolePermissions = [
            'Admin' => $inventoryPermissions, // Admin gets all permissions
            'Branch Head' => $inventoryPermissions, // Branch Head gets all permissions
            'Manager' => [
                'View Inventory Dashboard',
                'Configure Inventory',
                'Manage Inventory Allocation',
                'Create Tray',
                'Update Tray',
                'Create Slot',
                'Update Slot',
                'Allocate Inventory',
                'Adjust Inventory',
            ],
            'Staff' => [
                'View Inventory Dashboard',
                'Allocate Inventory',
                'Adjust Inventory',
            ],
        ];

        // Assign permissions to roles
        foreach ($rolePermissions as $roleName => $permissions) {
            // Find roles that match the pattern (roles are restaurant-specific)
            $roles = Role::where('name', 'like', $roleName . '_%')->get();
            
            foreach ($roles as $role) {
                $this->command->info("Assigning inventory permissions to role: {$role->name}");
                
                // Sync permissions (this will add new permissions and keep existing ones)
                $existingPermissions = $role->permissions->pluck('name')->toArray();
                $allPermissions = array_unique(array_merge($existingPermissions, $permissions));
                
                $role->syncPermissions($allPermissions);
            }
        }

        $this->command->info('Inventory permissions assigned to roles successfully.');
    }
}