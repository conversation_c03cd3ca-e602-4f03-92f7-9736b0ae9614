<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Traits\HasBranch;
use App\Traits\BranchScoped;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Tray extends BaseModel
{
    use HasFactory, HasBranch, BranchScoped;

    protected $fillable = [
        'branch_id',
        'name',
        'sort_order'
    ];

    protected $casts = [
        'sort_order' => 'integer',
    ];

    /**
     * Validation rules for tray creation and updates
     */
    public static function validationRules($trayId = null): array
    {
        return [
            'branch_id' => 'required|exists:branches,id',
            'name' => [
                'required',
                'string',
                'max:255',
                'unique:trays,name,' . $trayId . ',id,branch_id,' . request('branch_id')
            ],
            'sort_order' => 'nullable|integer|min:0'
        ];
    }

    /**
     * Get validation messages
     */
    public static function validationMessages(): array
    {
        return [
            'branch_id.required' => 'Branch is required.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'name.required' => 'Tray name is required.',
            'name.string' => 'Tray name must be a string.',
            'name.max' => 'Tray name cannot exceed 255 characters.',
            'name.unique' => 'A tray with this name already exists in this branch.',
            'sort_order.integer' => 'Sort order must be an integer.',
            'sort_order.min' => 'Sort order cannot be negative.'
        ];
    }

    /**
     * Get the branch that owns this tray
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get all slots for this tray
     */
    public function slots(): HasMany
    {
        return $this->hasMany(Slot::class)->orderBy('sort_order');
    }

    /**
     * Get total capacity across all slots in this tray
     */
    public function getTotalCapacity(): int
    {
        return $this->slots()->sum('capacity');
    }

    /**
     * Get current inventory across all slots in this tray
     */
    public function getCurrentInventory(): int
    {
        return $this->slots()
            ->join('inventory_allocations', 'slots.id', '=', 'inventory_allocations.slot_id')
            ->sum('inventory_allocations.current_quantity');
    }

    /**
     * Get remaining capacity across all slots in this tray
     */
    public function getRemainingCapacity(): int
    {
        return $this->getTotalCapacity() - $this->getCurrentInventory();
    }

    /**
     * Check if tray has any occupied slots
     */
    public function hasOccupiedSlots(): bool
    {
        return $this->slots()->whereHas('inventoryAllocation')->exists();
    }

    /**
     * Get capacity utilization percentage
     */
    public function getCapacityUtilization(): float
    {
        $totalCapacity = $this->getTotalCapacity();
        if ($totalCapacity === 0) {
            return 0.0;
        }
        
        return ($this->getCurrentInventory() / $totalCapacity) * 100;
    }
}