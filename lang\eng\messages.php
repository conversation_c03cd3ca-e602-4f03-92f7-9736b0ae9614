<?php

return array(
    'menuAdded' => 'Menu added successfully!',
    'noMenuAdded' => 'No menu added!',
    'menuUpdated' => 'Menu updated successfully!',
    'menuDeleted' => 'Menu deleted successfully!',
    'categoryAdded' => 'Item category added successfully!',
    'categoryUpdated' => 'Item category updated successfully!',
    'menuItemAdded' => 'Menu item added successfully!',
    'menuItemUpdated' => 'Menu item updated successfully!',
    'menuItemDeleted' => 'Menu item deleted successfully!',
    'menuItemCategoryDeleted' => 'Menu item category deleted successfully!',
    'itemVariationDeleted' => 'Item variation deleted successfully!',
    'noItemAdded' => 'No record found',
    'noItemCategoryAdded' => 'No item category added',
    'noAreaAdded' => 'No area is added.',
    'areaAdded' => 'Area added successfully!',
    'areaUpdated' => 'Area updated successfully!',
    'areaDeleted' => 'Area deleted successfully!',
    'noTableadded' => 'No table is added.',
    'tableAdded' => 'Table added successfully!',
    'tableUpdated' => 'Table updated successfully!',
    'tableDeleted' => 'Table deleted successfully!',
    'setTableNo' => 'You need to set the table number',
    'enterPax' => 'Enter the no of Pax.',
    'selectWaiter' => 'Select the waiter who took the order.',
    'orderItemRequired' => 'You need to add items to the order.',
    'orderSaved' => 'Order placed successfully.',
    'kotGenerated' => 'KOT Generated Successfully.',
    'billedSuccess' => 'Order Billed Successfully.',
    'addCustomerDetails' => 'Add customer details.',
    'thankYouVisit' => 'Thank you for your visit!',
    'emailVerificationCode' => 'Email verification code.',
    'invalidVerificationCode' => 'Invalid verification code.',
    'verificationCodeSent' => 'Verification Code Sent',
    'customerDeleted' => 'Customer deleted successfully!',
    'noCustomerFound' => 'No Customer Found',
    'settingsUpdated' => 'Settings updated successfully!',
    'orderPlacedSuccess' => 'Order placed successfully!',
    'orderCanceled' => 'Order canceled successfully!',
    'noPaymentFound' => 'No Payment Found',
    'waitingTodayOrder' => ' Waiting for the today\'s first order &#x23F3;',
    'updateAlert' => 'Do not click update now button if the application is customized. Your changes will be lost.',
    'updateBackupNotice' => 'Take a backup of files and database before updating.',
    'frontHeroHeading' => 'Ready to Satisfy Your Cravings? Place Your Order Now!',
    'profileUpdated' => 'Profile Updated Successfully',
    'cartEmpty' => 'Your cart is empty 🙁',
    'memberAdded' => 'Member added successfully!',
    'cannotEditOwnRole' => 'You cannot change own role.',
    'memberUpdated' => 'Member updated successfully!',
    'memberDeleted' => 'Member deleted successfully!',
    'currencyDeleted' => 'Currency deleted successfully!',
    'noCurrencyFound' => 'No currency found.',
    'cannotDeleteDefaultCurrency' => 'Cannot Delete Default Currency.',
    'taxApplicableInfo' => 'All taxes will be applicable on creating order.',
    'taxDeleted' => 'Tax deleted successfully.',
    'chooseEndTimeLater' => 'Choose and end time later than the start time.',
    'frontReservationHeading' => 'Book a Table and Savor the Experience.',
    'selectBookingDetail' => 'Select your booking details',
    'selectTimeSlot' => 'Select Time Slot',
    'loginForReservation' => 'Login to make reservation',
    'reservationConfirmed' => 'Your reservation is confirmed.',
    'noTimeSlot' => 'No time slot available. Choose a different date or meal time.',
    'noTableReserved' => 'No table is reserved.',
    'smtpError' => 'Your SMTP details are not correct. Please update to the correct one.<br><br>Proper SMTP configuration is essential for sending emails such as order confirmations, password resets, and notifications to customers and staff. Without correct SMTP settings, your vending machine management system cannot communicate via email.',
    'newOrderReceived' => 'New Order Received.',
    'cannotDeleteCurrentBranch' => 'Cannot delete current vending machine.',
    'branchAdded' => 'New Vending machine added.',
    'branchDeleted' => 'Vending machine deleted successfully.',
    'branchUpdated' => 'Vending machine updated successfully.',
    'licenseUpgraded' => 'License upgraded successfully.',
    'noRestaurantFound' => 'No company found.',
    'restaurantUpdated' => 'Company updated successfully!',
    'invalidStripePlan' => 'Invalid Stripe Plan.',
    'packageUpdated' => 'Package updated successfully!',
    'noPackageFound' => 'No package found.',
    'alreadyRequestPending' => 'You have already raised a request.',
    'packageAdded' => 'Package added successfully!',
    'packageDeleted' => 'Package deleted successfully!',
    'smtpRecommendation' => 'Recommendation for SMTP ',
    'noMemberFound' => 'No member found.',
    'noInvoiceFound' => 'No invoice found.',
    'loadingData' => 'Loading Data...',
    'gatewayNotAdded' => 'Payment gateway credentials not added by superadmin',
    'smtpSuccess' => 'Your SMTP details are correct',
    'packageRequired' => 'Package ID is required.',
    'packageNotFound' => 'Package not found.',
    'trialExpireOnRequired' => 'Trial expiration date is required.',
    'amountNumeric' => 'The amount must be a number.',
    'payDateRequired' => 'Payment date is required.',
    'nextPayDateRequired' => 'Next payment date is required.',
    'noOfflinePaymentMethodFound' => 'No offline payment method found.',
    'amountRequired' => 'Amount is required.',
    'licenceExpireRequired' => 'License expiration date is required.',
    'offlinePaymentMethodAdded' => 'Offline payment method added successfully!',
    'offlinePaymentMethodUpdated' => 'Offline payment method updated successfully!',
    'noOfflinePaymentRequestFound' => 'No offline payment request found.',
    'offlinePaymentMethodDeleted' => 'Offline payment method deleted successfully!',
    'offlinePaymentVerified' => 'Offline payment verified successfully!',
    'OfflinePlanChangeDeclined' => 'Offline plan change request declined successfully.',
    'noInvoiceFound' => 'No invoice found.',
    'planUpgraded' => 'Plan upgraded successfully!',
    'noPlanIdFound' => 'No plan id found.',
    'requestSubmittedSuccessfully' => 'Request submitted successfully.',
    'startShoppingNow' => 'Add items to your cart!',
    'smtpSecureEnabled' => 'For Gmail SMTP configuration, please follow the following link ',
    'testEmailSuccess' => 'Email sent successfully!',
    'testEmailError' => 'Failed to send test email: :message',
    'waiterRequestCompleted' => 'Request completed successfully!',
    'welcomeToUpgrade' => 'Please upgrade your license to access premium features and enhance your vending machine management experience.',
    'upgradeRequired' => 'Upgrade Required.',
    'noAdminFound' => 'No Admin Found',
    'statusUpdated' => 'Status updated successfully!',
    'invalidRequest' => 'Invalid Request',
    'packageIsUsed' => 'Package is used by some companys. First change the package for those companys.',
    'currencyCannotBeDeleted' => 'Currency cannot be deleted',
    'currencyIsUsedInPackages' => 'Currency is used in some packages. First change the currency for those packages or delete the packages with this currency.',
    'statusUpdated' => 'Status updated successfully!',
    'invalidRequest' => 'Invalid Request',
    'statusUpdated' => 'Status updated successfully!',
    'invalidRequest' => 'Invalid Request',
    'languageUpdated' => 'Language updated successfully!',
    'fileUploaded' => 'File uploaded successfully!',
    'noFilesAvailable' => 'No files available',
    'noFilesAvailableDescription' => 'There are no files to display at the moment.',
    'transferProgress' => 'Transfer Progress',
    'discountPercentError' => 'The discount percentage cannot exceed 100%.',
    'branchLimitReached' => 'Vending machine limit reached, can\'t add more with the current package.',
    'ModifierGroupAdded' => 'Modifier Group added successfully!',
    'noModifierGroupFound' => 'No modifier group found.',
    'itemModifierGroupAdded' => 'Item Modifier Group added successfully!',
    'itemModifierGroupUpdated' => 'Item Modifier Group updated successfully!',
    'itemModifierGroupDeleted' => 'Item Modifier Group deleted successfully!',
    'noItemModifierFound' => 'No item modifier found.',
    'ModifierGroupUpdated' => 'Modifier Group updated successfully!',
    'cronIsNotRunning' => 'It appears that your cron job has not run in the last 48 hours. Please check to ensure that it is properly configured. This message will automatically disappear once the cron job is functioning correctly again',
    'cronIsNotRunningOnboarding' => 'Please check to ensure that it is properly configured. This message will automatically disappear once the cron job is functioning correctly again',
    'cronJobSetting' => 'Cron Job Setting',
    'cronJobSettingDescription' => 'Click here to view the cron job setting.',
    'downloadFilefromCodecanyon' => 'Download the zip file from codecanyon and upload it here. Only .zip file type is allowed for uploading.',
    'sweetAlertTitle' => 'Are you sure?',
    'removeFileText' => 'You will not be able to recover the deleted file!',
    'confirmDelete' => 'Yes, delete it!',
    'deleteSuccess' => 'Deleted successfully.',
    'installingUpdateMessage' => 'Installing...Please wait (This may take a few minutes.)',
    'installedUpdateMessage' => 'Installed successfully. Reload the page to see the changes.',
    'customModuleInstalled' => 'Module installed successfully. You will be redirected to the custom module page. Activate the module to use it.',
    'noRecordFound' => 'No record found',
    'orderDeleted' => 'Order deleted successfully!',
    'CartAddPermissionDenied' => 'Permission denied. Please contact the company.',
    'noExpensesAdded' => 'No Expenses Found',
    'expenseAdded' => 'Expenses Added ',
    'expenseUpdated' => 'Expenses Updated ',
    'showHidePurchaseCode' => 'Show/Hide Purchase Code',
    'changePurchaseCode' => 'Change Purchase Code',
    'restaurantDeleted' => 'Company deleted successfully!',
    'deliveryExecutiveAssigned' => 'Delivery executive assigned!',
    'vendorUpdated' => 'Vendor Updated',
    'expenseDeleted' => 'Expense Deleted',
    'vendorDeleted' => 'Vendor Deleted',
    'noVendorsAdded' => 'No Vendors Found',
    'moduleSettingsInstall' => 'Install new modules to enhance your application\'s functionality',
    'chargeDeleted' => 'Charge deleted successfully!',
    'addonDescription' => 'This is an additional module that can be purchased separately.',
    'noChargeFound' => 'No charge found.',
    'paymentQrCodeRequired' => 'Payment QR Code is required.',
    'noSubscriptionFound' => 'No subscription found.',
    'subscriptionCancelled' => 'Subscription cancelled successfully!',
    'subscriptionUpdated' => 'Subscription updated successfully!',
    'paymentQrCodeRequired' => 'Payment QR Code is required.',
    'customerAdded' => 'Customer added successfully!',
    'customerAlreadyExists' => 'Customer with email :email already exists.',
    'customerImported' => 'Customers imported successfully!',
    'noFeatures' => 'No additional features found.',
    'paymentGatewayNotConfigured' => 'Payment gateway not configured.',
    'tipRemovedSuccessfully' => 'Tip removed successfully',
    'tipUpdatedSuccessfully' => 'Tip updated successfully',
    'tipAddedSuccessfully' => 'Tip added successfully',
    'paymentDoneSuccessfully' => 'Payment done successfully',
    'paymentFailed' => 'Payment failed, please try again',
    'invalidPaymentMethod' => 'Invalid payment method',
    'expenseCategoryDeleted' => 'Expense category deleted successfully!',
    'expenseCategoryAdded' => 'Expense category added successfully!',
    'expenseCategoryUpdated' => 'Expense category updated successfully!',
    'installAppInstruction' => 'To install this web app on your phone: tap  ',
    'addToHomeScreen' => 'and then Add to Home Screen',
    'confrmDeleteDyanamicMenu' => 'Are you sure you want to delete this Custom Menu',
    'noCustomMenu' => 'No Custom Menu Found',
    'menuUpdate' => 'Menu updated successfully!',
    'orderNotFound' => 'Order not found',
    'notHavePermission' => 'Access denied. Please reload page and try again.',
    'troubleClickingButton' => 'If you\'re having trouble clicking the ":actionText" button, copy and paste the URL below into your web browser:',
    'accountRecoveryAuthMessage' => 'Please confirm access to your account by entering the authentication code provided by your authenticator application.',
    'accountRecoveryCode' => 'Please confirm access to your account by entering one of your emergency recovery codes.',
    'invalidFlutterwavePlan' => 'Invalid Flutterwave plan ID.',
    'flutterwavePlanNotFound' => 'Flutterwave plan ID not found.',
    'FlutterwavePaymentError' => 'Flutterwave payment error: :message',
    'transactionReferenceMissing' => 'Transaction reference is missing.',
    'invalidTransactionReference' => 'Invalid transaction reference.',
    'paymentVerificationFailed' => 'Payment verification failed.',
    'cannotUseSelfDomain' => 'You cannot use your own domain as the landing site URL.',
    'charactersShowingPerLine' => 'How many characters showing per line in your paper, eg: in 80mm characters per line is 46',
    'printerIPAddress' => 'You may get Printer IP address from test print paper as per printer setting',
    'printerPortAddress' => 'In maximum case the Printer Port Address is 9100 but in case it is different please do a test print from your printer after turning it on, you will get the Printer Port Address in that test print paper',
    'printerAdded' => 'Printer added successfully!',
    'printerUpdated' => 'Printer updated successfully!',
    'printerDeleted' => 'Printer deleted successfully!',
    'featureDeleted' => 'Feature deleted successfully!',
    'reviewDeleted' => 'Review deleted successfully!',
    'faqAdd' => 'FAQ Add successfully!',
    'faqUpdated' => 'FAQ updated successfully!',
    'faqDeleted' => 'FAQ deleted successfully!',
    'contactSetting' => 'Contact setting updated successfully!',
    'headerSetting' => 'Heading updated successfully!',
    'waiterUpdated' => 'Waiter Updated successfully!',
    'errorWantToCreateNewKot' => 'To add an item, please create a new KOT by clicking the "New KOT" button.',
    'noPrinterAdded' => 'No printer added yet',
    'kotConflict' => 'The selected KOTs are already assigned to another printer. Please choose different KOTs.',
    'orderConflict' => 'The selected orders are already assigned to another printer. Please choose different orders.',
    'invalidCoordinates' => 'Invalid coordinates provided.',
    'deliverySettingInvalid' => 'Delivery settings are invalid. Please setup the vending machine.',
    'outOfDeliveryRange' => 'The delivery address is out of the delivery range.',
    'loginRequired' => 'Login required, Please login to continue.',
    'addressUpdated' => 'Address updated successfully!',
    'addressAdded' => 'Address added successfully!',
    'addressDeleted' => 'Address deleted successfully!',
    'noBranchCoordinates' => 'Delivery settings require vending machine coordinates. Please update your machine location first.',
    'noReservationsFound' => 'No reservations found.',
    'flutterwavePlanNotFound' => 'Flutterwave plan not found.',
    'paymentError' => 'Payment failed. Please try again.',
    'errorOccurred' => 'An error occurred. Please try again later.',
    'subscriptionStatusUpdate' => 'Subscription status updated in our system. Please log in to your PayFast account to complete the cancellation.',
    'noActiveKotPrinterConfigured' => 'No active KOT printer configured.',
    'defaultPrinterExists' => 'Default printer already exists.',
    'printerNotConnected' => ' Printer Not Connected',
    'sessionDriverTooltip' => 'This option controls the session "driver" that will be used on requests. Database driver gives you more control. * Changing the driver will make you logout.',
    'atLeastTwoStatusesRequired' => 'At least two statuses are required.',
    'paymentGatewaySettingsUpdated' => 'Payment gateway settings updated successfully!',
    'cannotDeleteDefaultPrinter' => 'You Cannot Delete Default Printer',
    'printerStatusUpdated' => 'Printer status updated successfully!',
    'cannotUpdateDefaultPrinterStatus' => 'You cannot update the status of the default printer.',
    'noBranchFound' => 'No vending machine found. Please create a machine first.',
    'planNotFound' => 'Plan not found.',
);

