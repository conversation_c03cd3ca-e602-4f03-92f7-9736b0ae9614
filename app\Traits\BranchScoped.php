<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

trait BranchScoped
{
    /**
     * Boot the trait and add global scope for branch isolation.
     */
    protected static function bootBranchScoped(): void
    {
        static::addGlobalScope('branch', function (Builder $builder) {
            $user = auth()->user();
            
            if ($user && $user->branch_id) {
                $model = $builder->getModel();
                
                // Check if model has direct branch_id column
                if ($model->getConnection()->getSchemaBuilder()->hasColumn($model->getTable(), 'branch_id')) {
                    $builder->where('branch_id', $user->branch_id);
                } 
                // For models like Slot that get branch through tray relationship
                elseif ($model->getTable() === 'slots') {
                    $builder->whereHas('tray', function ($query) use ($user) {
                        $query->where('branch_id', $user->branch_id);
                    });
                }
            }
        });
    }

    /**
     * Scope query to specific branch.
     */
    public function scopeForBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Check if the model belongs to the current user's branch.
     */
    public function belongsToUserBranch(): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }
        
        // Check if model has direct branch_id
        if (isset($this->branch_id)) {
            return $this->branch_id === $user->branch_id;
        }
        
        // For models like Slot that get branch through tray relationship
        if ($this->getTable() === 'slots' && $this->tray) {
            return $this->tray->branch_id === $user->branch_id;
        }
        
        return false;
    }
}