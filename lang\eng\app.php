<?php

return array(
    'close' => 'Close',
    'cancel' => 'Cancel',
    'save' => 'Save',
    'saved' => 'Saved',
    'saveTax' => 'Save Tax',
    'saveAndNew' => 'Save and Add New',
    'view' => 'View',
    'verify' => 'Verify',
    'accept' => 'Accept',
    'decline' => 'Decline',
    'update' => 'Update',
    'delete' => 'Delete',
    'action' => 'Action',
    'submit' => 'Submit',
    'pending' => 'Pending',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'verified' => 'Verified',
    'showing' => 'Showing',
    'to' => 'To',
    'of' => 'of',
    'results' => 'results',
    'clearFilter' => 'Clear Filters',
    'hideFilter' => 'Hide Filters',
    'showFilter' => 'Show Filters',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'status' => 'Status',
    'showAll' => 'Show All',
    'clear' => 'Clear',
    'error' => 'Error',
    'print' => 'PRINT',
    'dateTime' => 'Date & Time',
    'change' => 'Change',
    'add' => 'Add',
    'menu' => 'Menu',
    'createAccount' => 'Create a Account',
    'email' => 'Enter your email',
    'name' => 'Name',
    'description' => 'Description',
    'login' => 'Login',
    'continue' => 'Continue',
    'verificationCode' => 'Send verification code',
    'next' => 'Next',
    'resendVerificatonCode' => 'Resend Verificaton Code',
    'logout' => 'Logout',
    'export' => 'Export',
    'logo' => 'Logo',
    'download' => 'Download',
    'dateRange' => 'Date Range',
    'select' => 'Select',
    'selectStartDate' => 'Select Start Date',
    'selectEndDate' => 'Select End Date',
    'customDateRange' => 'Custom Date Range',
    'today' => 'Today',
    'currentWeek' => 'Current Week',
    'lastWeek' => 'Last Week',
    'nextWeek' => 'Next Week',
    'last7Days' => 'Last 7 Days',
    'currentMonth' => 'Current Month',
    'lastMonth' => 'Last Month',
    'currentYear' => 'Current Year',
    'lastYear' => 'Last Year',
    'date' => 'Date',
    'time' => 'Time',
    'rightReserved' => 'All rights reserved.',
    'loading' => 'Loading...',
    'profileInfo' => 'Profile Information',
    'fullName' => 'Your Full Name',
    'role' => 'Role',
    'permission' => 'Permission',
    'user' => 'User',
    'minutes' => 'Minutes',
    'reserveNow' => 'Reserve Now',
    'specialRequest' => 'Any special request?',
    'yes' => 'Yes',
    'no' => 'No',
    'hello' => 'Hello',
    'regards' => 'Regards',
    'thanks' => 'Thanks',
    'addMore' => 'Add +',
    'addNew' => 'Add New',
    'allRightsReserved' => 'All Rights Reserved.',
    'test' => 'TEST',
    'live' => 'LIVE',
    'id' => 'ID',
    'reset' => 'Reset',
    'paidVia' => 'Paid via',
    'back' => 'Back',
    'generateCredentials' => 'Generate Credentials',
    'Monday' => 'Monday',
    'Tuesday' => 'Tuesday',
    'Wednesday' => 'Wednesday',
    'Thursday' => 'Thursday',
    'Friday' => 'Friday',
    'Saturday' => 'Saturday',
    'Sunday' => 'Sunday',
    'approx' => 'Approx',
    'visitLink' => 'Visit Link',
    'callWaiterConfirmation' => 'Do you want to notify a waiter?',
    'callWaiterNotification' => 'Waiter has been notified!',
    'callWaiter' => 'Call Waiter',
    'stopImpersonation' => 'Stop Impersonation',
    'impersonate' => 'Impersonate',
    'approvalStatus' => 'Approval Status',
    'approve' => 'Approve',
    'reject' => 'Reject',
    'example' => 'Example',
    'fileNotUploaded' => 'File not uploaded',
    'moduleVersion' => 'Version',
    'moduleUpdateMessage' => 'New update available for :name module. Please update to version :version',
    'newModuleUpdateMessage' => 'New update available for <strong>:name</strong> module.',
    'purchaseCode' => 'Purchase Code',
    'moduleSwitchMessage' => 'Activate or deactivate :name module',
    'updateModule' => 'Update Module',
    'notify' => 'Notify',
    'moduleNotifySwitchMessage' => 'This will hide/show new update message on dashboard for :name module',
    'verifyEnvato' => 'Verify Purchase Code',
    'findPurchaseCode' => 'Click this link to find your purchase code.',
    'moduleSettingsInstall' => 'Install/Update Module',
    'dropFileToUpload' => 'Drop file to upload',
    'uploadDate' => 'Upload Date',
    'rememberMe' => 'Remember me',
    'forgotPassword' => 'Forgot your password?',
    'password' => 'Password',
    'forgotPasswordMessage' => 'Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.',
    'sendPasswordResetLink' => 'Send Password Reset Link',
    'enable' => 'Enable',
    'professional' => 'Professional',
    'pastel' => 'Pastel',
    'warm' => 'Warm',
    'backToLogin' => 'Back to Login',
    'uploadFavIconAndroidhCrome192' => 'Upload Favicon for Android Chrome (192x192)',
    'uploadFavIconAndroidhCrome512' => 'Upload Favicon for Android Chrome (512x512)',
    'uploadFavIconAppleTouchIcon' => 'Upload Favicon for Apple Touch Icon',
    'uploadFavicon16' => 'Upload Favicon (16x16)',
    'uploadFavicon32' => 'Upload Favicon (32x32)',
    'favicon' => 'Upload Favicon',
    'addon' => 'Addon',
    'list' => 'List',
    'grid' => 'Grid',
    'layout' => 'Layout',
    'offlineMessage' => 'You are currently offline.',
    'loadPage' => 'We couldn\'t load the next page on this connection. Please try again.',
    'RetryConnection' => 'Retry Connection',
    'import' => 'Import',
    'downloadSample' => 'Download Sample File',
    'noResults' => 'No results found',
    'optional' => 'Optional',
    'other' => 'Other',
    'disable' => 'Disable',
    'selectLanguage' => 'Select Language',
    'upload' => 'Upload',
    'selectIcon' => 'Select Icon',
    'searchIcon' => 'Search Icon',
    'impersonateTooltip' => 'Impersonate to this business - Login as the business owner to view and manage their account. This allows administrators to troubleshoot issues, provide support, and make changes on behalf of the business without requiring their credentials.',
    'autoRefresh' => 'Auto Refresh',
    'seconds' => 'Seconds',
    'minute' => 'Minute',
    'uploadedNow' => 'Uploaded Now',
    'uploadedAgo' => 'Uploaded :time',
    'clickToInstall' => 'Click to Install',
    'removeFile' => 'Remove File',
    'search' => 'Search',
    'header' => 'Header',
    'footer' => 'Footer',
    'position' => 'Position',
);
