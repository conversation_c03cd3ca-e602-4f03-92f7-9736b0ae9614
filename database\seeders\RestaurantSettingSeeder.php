<?php

namespace Database\Seeders;

use App\Models\Branch;
use App\Models\Country;
use App\Models\Restaurant;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;

class RestaurantSettingSeeder extends Seeder
{

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $country = Country::where('countries_code', 'ET')->first();


        $restaurantNames = [
            'Vendsoft Company',
	];

        $count = count($restaurantNames);
	
        for ($i = 0; $i < $count; $i++) {
            $this->command->info('Seeding Restaurant: ' . ($i + 1));

            $companyName = $restaurantNames[$i] ?? fake()->company();

            $setting = new Restaurant();
            $setting->name = $companyName;
            $setting->address = 'Bole Wolo Sefer, Addis Abeba, Ethiopia';
            $setting->phone_number = '0941575252';
            $setting->timezone = 'Africa/Addis_Ababa';
            $setting->theme_hex = '#A78BFA';
            $setting->theme_rgb = '167, 139, 250';
            $setting->email = str()->slug($companyName, '.') . '@gebeya.top';
            $setting->country_id = $country->id;
            $setting->package_id = 1; // Assuming package_id is 1 for seeding
            $setting->package_type = 'annual';
            $setting->about_us = Restaurant::ABOUT_US_DEFAULT_TEXT;
            $setting->facebook_link = 'https://www.facebook.com/AxumLabs';
            $setting->instagram_link = 'https://www.instagram.com/';
            $setting->twitter_link = 'https://www.twitter.com/';
            $setting->save();

            $branch = new Branch();
            $branch->restaurant_id = $setting->id;
            $branch->name = "Bole Vend" ;
            $branch->address = "Bole Wolo Sefer, Addis Abeba" ;
            $branch->saveQuietly();
            $this->call(OnboardingSeeder::class, false, ['branch' => $branch]);
            $branch->generateQrCode();
            $this->addKotPlaces($branch);

            $branch->generateKotSetting();

            $branch = new Branch();
            $branch->restaurant_id = $setting->id;
            $branch->name = "Old Airport";
            $branch->address = "Laftp Mall,Old Airport,Addis Abeba" ;
            $branch->saveQuietly();
            $this->call(OnboardingSeeder::class, false, ['branch' => $branch]);
            $branch->generateQrCode();
            $this->addKotPlaces($branch);

            $branch->generateKotSetting();
        }
    }

    public function addKotPlaces($branch)
    {
        if (!$branch) {
            $this->command->warn(__('messages.noBranchFound'));
            return;
        }

        // Create default KOT place
        $kotPlace = $branch->kotPlaces()->create([
            'name' => 'Default Kitchen',
            'branch_id' => $branch->id,
            'printer_id' => null, // Will update after printer is created
            'type' => 'food',
            'is_active' => true,
            'is_default' => true,
        ]);

        // Create default order place
        $orderPlace = $branch->orderPlaces()->create([
            'name' => 'Default POS Terminal',
            'branch_id' => $branch->id,
            'printer_id' => null, // Will update after printer is created
            'type' => 'vegetarian',
            'is_active' => true,
            'is_default' => true,
        ]);

        // Create printer and assign KOT and Order place IDs
        $printer = $branch->printerSettings()->create([
            'name' => 'Default Thermal Printer',
            'restaurant_id' => $branch->restaurant_id,
            'branch_id' => $branch->id,
            'is_active' => true,
            'is_default' => true,
            'printing_choice' => 'browserPopupPrint',
            'kots' => json_encode([$kotPlace->id]),
            'orders' => json_encode([$orderPlace->id]),
            'type' => null,
            'char_per_line' => null,
            'print_format' => null,
            'invoice_qr_code' => null,
            'open_cash_drawer' => null,
            'ipv4_address' => null,
            'thermal_or_nonthermal' => null,
            'share_name' => null,
        ]);

        // Update KOT and Order place with printer_id
        $kotPlace->printer_id = $printer->id;
        $kotPlace->save();

        $orderPlace->printer_id = $printer->id;
        $orderPlace->save();
    }

}
