name: <PERSON><PERSON>

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  laravel-tests:

    runs-on: ubuntu-latest

    steps:
    - uses: shivammathur/setup-php@15c43e89cdef867065b0213be354c2841860869e
      with:
        php-version: '8.1'
    - uses: actions/checkout@v4
    - name: Login to Docker Hub (or your private registry)
      uses: docker/login-action@v2
      with:
        username: addisflava05
        password: ************************************
    - name: Build the Docker image and push
      run: COMMIT_SHA=$(git rev-parse --short HEAD) ;  docker build . --push  --file Dockerfile --tag addisflava05/qrmenu:latest --tag addisflava05/qrmenu:$COMMIT_SHA
