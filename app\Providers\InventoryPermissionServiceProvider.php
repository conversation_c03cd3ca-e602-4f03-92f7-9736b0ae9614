<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\Helpers\InventoryPermissionHelper;

class InventoryPermissionServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the permission service
        $this->app->singleton(\App\Services\InventoryPermissionService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load helper functions
        require_once app_path('Helpers/inventory_helpers.php');

        // Register Blade directives for inventory permissions
        Blade::if('canViewInventoryDashboard', function () {
            return InventoryPermissionHelper::canViewDashboard();
        });

        Blade::if('canConfigureInventory', function () {
            return InventoryPermissionHelper::canConfigureInventory();
        });

        Blade::if('canManageInventoryAllocation', function () {
            return InventoryPermissionHelper::canManageAllocation();
        });

        Blade::if('canCreateTray', function () {
            return InventoryPermissionHelper::canCreateTray();
        });

        Blade::if('canCreateSlot', function () {
            return InventoryPermissionHelper::canCreateSlot();
        });

        Blade::if('canAllocateInventory', function () {
            return InventoryPermissionHelper::canAllocateInventory();
        });

        Blade::if('canAdjustInventory', function () {
            return InventoryPermissionHelper::canAdjustInventory();
        });

        Blade::if('hasAnyInventoryPermission', function () {
            return InventoryPermissionHelper::hasAnyInventoryPermission();
        });
    }
}