<?php

namespace App\Services;

use App\Models\User;
use App\Models\Tray;
use App\Models\Slot;
use App\Models\InventoryAllocation;

class InventoryPermissionService
{
    /**
     * Check if user can view inventory dashboard
     */
    public function canViewDashboard(User $user): bool
    {
        return $user->can('View Inventory Dashboard') && $user->branch_id;
    }

    /**
     * Check if user can configure inventory (trays and slots)
     */
    public function canConfigureInventory(User $user): bool
    {
        return $user->can('Configure Inventory') && $user->branch_id;
    }

    /**
     * Check if user can manage inventory allocation
     */
    public function canManageAllocation(User $user): bool
    {
        return $user->can('Manage Inventory Allocation') && $user->branch_id;
    }

    /**
     * Check if user can create trays
     */
    public function canCreateTray(User $user): bool
    {
        return $user->can('Create Tray') && $user->branch_id;
    }

    /**
     * Check if user can update a specific tray
     */
    public function canUpdateTray(User $user, Tray $tray): bool
    {
        return $user->can('Update Tray') && 
               $user->branch_id === $tray->branch_id;
    }

    /**
     * Check if user can delete a specific tray
     */
    public function canDeleteTray(User $user, Tray $tray): bool
    {
        return $user->can('Delete Tray') && 
               $user->branch_id === $tray->branch_id &&
               !$tray->hasOccupiedSlots();
    }

    /**
     * Check if user can create slots
     */
    public function canCreateSlot(User $user): bool
    {
        return $user->can('Create Slot') && $user->branch_id;
    }

    /**
     * Check if user can update a specific slot
     */
    public function canUpdateSlot(User $user, Slot $slot): bool
    {
        return $user->can('Update Slot') && 
               $user->branch_id === $slot->tray->branch_id;
    }

    /**
     * Check if user can delete a specific slot
     */
    public function canDeleteSlot(User $user, Slot $slot): bool
    {
        return $user->can('Delete Slot') && 
               $user->branch_id === $slot->tray->branch_id &&
               !$slot->isOccupied();
    }

    /**
     * Check if user can allocate inventory to slots
     */
    public function canAllocateInventory(User $user): bool
    {
        return $user->can('Allocate Inventory') && $user->branch_id;
    }

    /**
     * Check if user can adjust inventory quantities
     */
    public function canAdjustInventory(User $user): bool
    {
        return $user->can('Adjust Inventory') && $user->branch_id;
    }

    /**
     * Check if user can access a specific inventory allocation
     */
    public function canAccessAllocation(User $user, InventoryAllocation $allocation): bool
    {
        return $user->branch_id === $allocation->slot->tray->branch_id;
    }

    /**
     * Get all inventory permissions for a user
     */
    public function getUserInventoryPermissions(User $user): array
    {
        $permissions = [
            'view_dashboard' => $this->canViewDashboard($user),
            'configure_inventory' => $this->canConfigureInventory($user),
            'manage_allocation' => $this->canManageAllocation($user),
            'create_tray' => $this->canCreateTray($user),
            'create_slot' => $this->canCreateSlot($user),
            'allocate_inventory' => $this->canAllocateInventory($user),
            'adjust_inventory' => $this->canAdjustInventory($user),
        ];

        return $permissions;
    }

    /**
     * Check if user has any inventory permissions
     */
    public function hasAnyInventoryPermission(User $user): bool
    {
        $permissions = $this->getUserInventoryPermissions($user);
        return in_array(true, $permissions, true);
    }
}