<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Traits\HasRestaurant;
use App\Traits\GeneratesQrCode;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\HasBranch;
use Spatie\LaravelPackageTools\Concerns\Package\HasServiceProviders;

class Branch extends BaseModel
{
    use HasFactory;
    use GeneratesQrCode;
    use HasRestaurant;

    protected $guarded = ['id'];

    protected $casts = [
        'lat' => 'float',
        'lng' => 'float',
    ];

    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    public function getQrCodeFileName(): string
    {
        return 'qrcode-branch-' . $this->id . '-' . $this->restaurant->id . '.png';
    }

    public function getRestaurantId(): int
    {
        return $this->restaurant_id;
    }

    public function generateQrCode()
    {
        // $this->createQrCode(route('table_order', [$this->getRestaurantId()]) . '?branch=' . $this->id);
        $this->createQrCode(route('table_order', [$this->restaurant_id]) . '?branch=' . $this->id . '&hash=' . $this->restaurant->hash . '&from_qr=1');
    }

    public function deliverySetting()
    {
        return $this->hasOne(BranchDeliverySetting::class, 'branch_id');
    }

    public function deliveryFeeTiers()
    {
        return $this->hasMany(DeliveryFeeTier::class);
    }

    public function qRCodeUrl(): Attribute
    {
        return Attribute::get(fn(): string => asset_url_local_s3('qrcodes/' . $this->getQrCodeFileName()));
    }

    public function printerSettings(): HasMany
    {
        return $this->hasMany(Printer::class);
    }

    public function kotPlaces(): HasMany
    {
        return $this->hasMany(KotPlace::class);
    }

    public function orderPlaces(): HasMany
    {
        return $this->hasMany(MultipleOrder::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function kotSetting(): HasOne
    {
        return $this->hasOne(KotSetting::class);
    }

    public function generateKotSetting()
    {
        $this->kotSetting()->create([
            'branch_id' => $this->id,
            'default_status' => 'pending',
            'enable_item_level_status' => true,
        ]);
    }

    /**
     * Get all trays for this branch
     */
    public function trays(): HasMany
    {
        return $this->hasMany(Tray::class)->orderBy('sort_order');
    }

    /**
     * Get total inventory capacity across all trays and slots
     */
    public function getTotalInventoryCapacity(): int
    {
        return $this->trays()
            ->join('slots', 'trays.id', '=', 'slots.tray_id')
            ->sum('slots.capacity');
    }

    /**
     * Get current inventory utilization across all trays and slots
     */
    public function getCurrentInventoryUtilization(): float
    {
        $totalCapacity = $this->getTotalInventoryCapacity();
        if ($totalCapacity === 0) {
            return 0.0;
        }

        $currentInventory = $this->trays()
            ->join('slots', 'trays.id', '=', 'slots.tray_id')
            ->join('inventory_allocations', 'slots.id', '=', 'inventory_allocations.slot_id')
            ->sum('inventory_allocations.current_quantity');

        return ($currentInventory / $totalCapacity) * 100;
    }

    /**
     * Get total number of occupied slots in this branch
     */
    public function getOccupiedSlotsCount(): int
    {
        return $this->trays()
            ->join('slots', 'trays.id', '=', 'slots.tray_id')
            ->whereExists(function ($query) {
                $query->select('id')
                    ->from('inventory_allocations')
                    ->whereColumn('inventory_allocations.slot_id', 'slots.id');
            })
            ->count();
    }

    /**
     * Get total number of available slots in this branch
     */
    public function getTotalSlotsCount(): int
    {
        return $this->trays()
            ->join('slots', 'trays.id', '=', 'slots.tray_id')
            ->count();
    }
}
