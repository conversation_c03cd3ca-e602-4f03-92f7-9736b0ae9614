<?php
namespace Database\Seeders;

use App\Models\Module;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Retrieve modules by name
        $menuModule = Module::where('name', 'Menu')->first();
        $menuItemModule = Module::where('name', 'Menu Item')->first();
        $itemCategoryModule = Module::where('name', 'Item Category')->first();
        $areaModule = Module::where('name', 'Area')->first();
        $tableModule = Module::where('name', 'Table')->first();
        $reservationModule = Module::where('name', 'Reservation')->first();
        $kotModule = Module::where('name', 'KOT')->first();
        $orderModule = Module::where('name', 'Order')->first();
        $customerModule = Module::where('name', 'Customer')->first();
        $staffModule = Module::where('name', 'Staff')->first();
        $paymentModule = Module::where('name', 'Payment')->first();
        $reportModule = Module::where('name', 'Report')->first();
        $settingsModule = Module::where('name', 'Settings')->first();
        $deliveryExecutiveModule = Module::where('name', 'Delivery Executive')->first();
        $waiterRequestModule = Module::where('name', 'Waiter Request')->first();
        $expenseModule = Module::where('name', 'Expense')->first();
        $vendorModule = Module::where('name', 'Vendor')->first();
        $expenseCategoryModule = Module::where('name', 'Expense Category')->first();
        // Define permissions to insert
        $permissions = [
            ['guard_name' => 'web', 'name' => 'Create Menu', 'module_id' => $menuModule->id],
            ['guard_name' => 'web', 'name' => 'Show Menu', 'module_id' => $menuModule->id],
            ['guard_name' => 'web', 'name' => 'Update Menu', 'module_id' => $menuModule->id],
            ['guard_name' => 'web', 'name' => 'Delete Menu', 'module_id' => $menuModule->id],

            ['guard_name' => 'web', 'name' => 'Create Menu Item', 'module_id' => $menuItemModule->id],
            ['guard_name' => 'web', 'name' => 'Show Menu Item', 'module_id' => $menuItemModule->id],
            ['guard_name' => 'web', 'name' => 'Update Menu Item', 'module_id' => $menuItemModule->id],
            ['guard_name' => 'web', 'name' => 'Delete Menu Item', 'module_id' => $menuItemModule->id],

            ['guard_name' => 'web', 'name' => 'Create Item Category', 'module_id' => $itemCategoryModule->id],
            ['guard_name' => 'web', 'name' => 'Show Item Category', 'module_id' => $itemCategoryModule->id],
            ['guard_name' => 'web', 'name' => 'Update Item Category', 'module_id' => $itemCategoryModule->id],
            ['guard_name' => 'web', 'name' => 'Delete Item Category', 'module_id' => $itemCategoryModule->id],



            ['guard_name' => 'web', 'name' => 'Create Order', 'module_id' => $orderModule->id],
            ['guard_name' => 'web', 'name' => 'Show Order', 'module_id' => $orderModule->id],
            ['guard_name' => 'web', 'name' => 'Update Order', 'module_id' => $orderModule->id],
            ['guard_name' => 'web', 'name' => 'Delete Order', 'module_id' => $orderModule->id],

            ['guard_name' => 'web', 'name' => 'Create Customer', 'module_id' => $customerModule->id],
            ['guard_name' => 'web', 'name' => 'Show Customer', 'module_id' => $customerModule->id],
            ['guard_name' => 'web', 'name' => 'Update Customer', 'module_id' => $customerModule->id],
            ['guard_name' => 'web', 'name' => 'Delete Customer', 'module_id' => $customerModule->id],

            ['guard_name' => 'web', 'name' => 'Create Staff Member', 'module_id' => $staffModule->id],
            ['guard_name' => 'web', 'name' => 'Show Staff Member', 'module_id' => $staffModule->id],
            ['guard_name' => 'web', 'name' => 'Update Staff Member', 'module_id' => $staffModule->id],
            ['guard_name' => 'web', 'name' => 'Delete Staff Member', 'module_id' => $staffModule->id],

            ['guard_name' => 'web', 'name' => 'Create Delivery Executive', 'module_id' => $deliveryExecutiveModule->id],
            ['guard_name' => 'web', 'name' => 'Show Delivery Executive', 'module_id' => $deliveryExecutiveModule->id],
            ['guard_name' => 'web', 'name' => 'Update Delivery Executive', 'module_id' => $deliveryExecutiveModule->id],
            ['guard_name' => 'web', 'name' => 'Delete Delivery Executive', 'module_id' => $deliveryExecutiveModule->id],

            ['guard_name' => 'web', 'name' => 'Show Payments', 'module_id' => $paymentModule->id],

            ['guard_name' => 'web', 'name' => 'Show Reports', 'module_id' => $reportModule->id],

            ['guard_name' => 'web', 'name' => 'Manage Settings', 'module_id' => $settingsModule->id],


            ['guard_name' => 'web', 'name' => 'Create Expense', 'module_id' => $expenseModule->id],
            ['guard_name' => 'web', 'name' => 'Show Expense', 'module_id' => $expenseModule->id],
            ['guard_name' => 'web', 'name' => 'Update Expense', 'module_id' => $expenseModule->id],
            ['guard_name' => 'web', 'name' => 'Delete Expense', 'module_id' => $expenseModule->id],

            ['guard_name' => 'web', 'name' => 'Create Expense Category', 'module_id' => $expenseModule->id],
            ['guard_name' => 'web', 'name' => 'Show Expense Category', 'module_id' => $expenseModule->id],
            ['guard_name' => 'web', 'name' => 'Update Expense Category', 'module_id' => $expenseModule->id],
            ['guard_name' => 'web', 'name' => 'Delete Expense Category', 'module_id' => $expenseModule->id],

            // Note: Inventory permissions are handled by migration 2025_07_25_012949_add_inventory_permissions.php

        ];

        // Insert permissions into the database, avoiding duplicates
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                [
                    'name' => $permission['name'],
                    'guard_name' => $permission['guard_name']
                ],
                $permission
            );
        }
    }

}
