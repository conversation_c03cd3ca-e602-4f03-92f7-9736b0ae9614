<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BranchDataIsolation
{
    /**
     * Handle an incoming request to ensure branch-specific data isolation.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();

        if (!$user) {
            abort(401, 'Unauthorized');
        }

        // Ensure user has a branch assigned
        if (!$user->branch_id) {
            abort(403, 'No branch assigned to user');
        }

        // Add branch_id to request for automatic filtering
        $request->merge(['user_branch_id' => $user->branch_id]);

        return $next($request);
    }
}