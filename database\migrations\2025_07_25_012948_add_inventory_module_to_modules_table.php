<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Module;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add Inventory module to modules table
        Module::create(['name' => 'Inventory']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove Inventory module from modules table
        Module::where('name', 'Inventory')->delete();
    }
};