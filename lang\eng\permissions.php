<?php

return [
    'permissions' => [
        'Create Menu' => 'Create Menu',
        'Show Menu' => 'Show Menu',
        'Update Menu' => 'Update Menu',
        'Delete Menu' => 'Delete Menu',
        'Create Menu Item' => 'Create Item',
        'Show Menu Item' => 'Show Item',
        'Update Menu Item' => 'Update Item',
        'Delete Menu Item' => 'Delete Item',
        'Create Item Category' => 'Create Item Category',
        'Show Item Category' => 'Show Item Category',
        'Update Item Category' => 'Update Item Category',
        'Delete Item Category' => 'Delete Item Category',
        'Manage KOT' => 'Manage KOT',
        'Create Order' => 'Create Order',
        'Show Order' => 'Show Order',
        'Update Order' => 'Update Order',
        'Delete Order' => 'Delete Order',
        'Create Customer' => 'Create Customer',
        'Show Customer' => 'Show Customer',
        'Update Customer' => 'Update Customer',
        'Delete Customer' => 'Delete Customer',
        'Create Staff Member' => 'Create Staff Member',
        'Show Staff Member' => 'Show Staff Member',
        'Update Staff Member' => 'Update Staff Member',
        'Delete Staff Member' => 'Delete Staff Member',
        'Create Delivery Executive' => 'Create Delivery Executive',
        'Show Delivery Executive' => 'Show Delivery Executive',
        'Update Delivery Executive' => 'Update Delivery Executive',
        'Delete Delivery Executive' => 'Delete Delivery Executive',
        'Show Payments' => 'Show Payments',
        'Show Reports' => 'Show Reports',
        'Manage Settings' => 'Manage Settings',
        'Manage Waiter Request' => 'Manage Waiter Request',
        'Create Expense' => 'Create Expenses',
        'Show Expense' => 'Show Expenses',
        'Update Expense' => 'Update Expenses',
        'Delete Expense' => 'Delete Expenses',
        'Create Vendor' => 'Create Vendor',
        'Show Vendor' => 'Show Vendor',
        'Update Vendor' => 'Update Vendor',
     'Delete Vendor' => 'Delete Vendor',

        'Create Inventory Item' => 'Create Inventory Item',
        'Show Inventory Item' => 'Show Inventory Item',
        'Update Inventory Item' => 'Update Inventory Item',
        'Delete Inventory Item' => 'Delete Inventory Item',
        'Create Inventory Movement' => 'Create Inventory Movement',
        'Show Inventory Movement' => 'Show Inventory Movement',
        'Update Inventory Movement' => 'Update Inventory Movement',
        'Delete Inventory Movement' => 'Delete Inventory Movement',
        'Create Unit' => 'Create Unit',
        'Show Unit' => 'Show Unit',
        'Update Unit' => 'Update Unit',
        'Delete Unit' => 'Delete Unit',
        'Create Recipe' => 'Create Recipe',
        'Show Recipe' => 'Show Recipe',
        'Update Recipe' => 'Update Recipe',
        'Delete Recipe' => 'Delete Recipe',
        'Create Purchase Order' => 'Create Purchase Order',
        'Show Purchase Order' => 'Show Purchase Order',
        'Update Purchase Order' => 'Update Purchase Order',
        'Delete Purchase Order' => 'Delete Purchase Order',
        'Show Report' => 'Show Report',
        'Update Settings' => 'Update Settings',
        'Show Inventory Stock' => 'Show Inventory Stock',
        'Show Inventory Report' => 'Show Inventory Report',
        'Update Inventory Settings' => 'Update Inventory Settings',
        'Show Supplier' => 'Show Supplier',
        'Create Supplier' => 'Create Supplier',
        'Update Supplier' => 'Update Supplier',
        'Delete Supplier' => 'Delete Supplier',
        'Create Expense Category' => 'Create Expense Category',
    'Show Expense Category' => 'Show Expense Category',
    'Update Expense Category' => 'Update Expense Category',
    'Delete Expense Category' => 'Delete Expense Category',
    ],

    'modules' => [
      'Menu' => 'Menu',
      'Menu Item' => 'Item',
      'Item Category' => 'Item Category',
      'KOT' => 'KOT',
      'Order' => 'Order',
      'Customer' => 'Customer',
      'Staff' => 'Staff',
      'Delivery Executive' => 'Delivery Executive',
      'Payment' => 'Payment',
      'Report' => 'Report',
      'Settings' => 'Settings',
      'Expense' => 'Expenses',
      'Vendor' => 'Vendor',

      'Inventory' => 'Inventory',
      'Change Branch' => 'Change Machine',
      'Export Report' => 'Export Report',
      'Payment Gateway Integration' => 'Payment Gateway Integration',
      'Theme Setting' => 'Theme Setting',
    ],


];
