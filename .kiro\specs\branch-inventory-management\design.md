# Design Document

## Overview

The branch inventory management system extends the existing Laravel application to provide configurable tray and slot-based inventory management for each branch. The system allows branch managers to define the physical storage structure (trays and slots) and manage menu item inventory within those constraints.

The design leverages the existing Branch and MenuItem models while introducing new models for Tray, Slot, and InventoryAllocation to manage the hierarchical storage structure and inventory tracking.

## Architecture

### Database Schema Design

The system introduces three new models that integrate with existing Branch and MenuItem models:

```
Branch (existing)
├── Tray (new) - multiple trays per branch
    ├── Slot (new) - multiple slots per tray
        └── InventoryAllocation (new) - tracks menu item inventory in slots
```

### Model Relationships

- **Branch → Tray**: One-to-Many (a branch has many trays)
- **Tray → Slot**: One-to-Many (a tray has many slots)  
- **Slot → InventoryAllocation**: One-to-One (a slot can hold only one specific menu item, but multiple units of that same menu item based on capacity)
- **MenuItem → InventoryAllocation**: One-to-Many (a menu item can be allocated to multiple slots)

## Components and Interfaces

### Models

#### Tray Model
```php
class Tray extends BaseModel
{
    protected $fillable = ['branch_id', 'name', 'sort_order'];
    
    public function branch(): BelongsTo
    public function slots(): HasMany
    public function getTotalCapacity(): int
    public function getCurrentInventory(): int
}
```

#### Slot Model
```php
class Slot extends BaseModel
{
    protected $fillable = ['tray_id', 'name', 'capacity', 'sort_order'];
    
    public function tray(): BelongsTo
    public function inventoryAllocation(): HasOne
    public function isOccupied(): bool
    public function getRemainingCapacity(): int
}
```

#### InventoryAllocation Model
```php
class InventoryAllocation extends BaseModel
{
    protected $fillable = ['slot_id', 'menu_item_id', 'current_quantity'];
    
    public function slot(): BelongsTo
    public function menuItem(): BelongsTo
    public function canAddQuantity(int $quantity): bool  // Check if adding quantity would exceed slot capacity
    public function addInventory(int $quantity): bool    // Add units of the same menu item
    public function removeInventory(int $quantity): bool // Remove units, ensuring quantity doesn't go below 0
    public function getRemainingCapacity(): int          // Calculate how many more units can fit
}
```

### Services

#### BranchInventoryService
Handles business logic for branch inventory operations:
- Tray configuration management
- Slot configuration management
- Inventory allocation and deallocation
- Capacity validation
- Inventory calculations

#### InventoryValidationService
Validates inventory operations:
- Capacity constraints
- Single menu item per slot constraint
- Minimum slot requirement per menu item

### Livewire Components

#### BranchInventoryConfiguration
- Manages tray and slot configuration for a branch
- Handles adding/removing trays and slots
- Validates configuration changes

#### InventoryAllocationManager
- Manages menu item allocation to slots
- Handles inventory quantity updates
- Displays current inventory status

#### InventoryDashboard
- Provides overview of branch inventory status
- Shows capacity utilization
- Highlights low stock or empty slots

## Data Models

### Database Tables

#### trays
```sql
CREATE TABLE trays (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    branch_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE
);
```

#### slots
```sql
CREATE TABLE slots (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tray_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    capacity INT NOT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (tray_id) REFERENCES trays(id) ON DELETE CASCADE
);
```

#### inventory_allocations
```sql
CREATE TABLE inventory_allocations (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    slot_id BIGINT UNSIGNED NOT NULL UNIQUE,
    menu_item_id BIGINT UNSIGNED NOT NULL,
    current_quantity INT DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (slot_id) REFERENCES slots(id) ON DELETE CASCADE,
    FOREIGN KEY (menu_item_id) REFERENCES menu_items(id) ON DELETE CASCADE
);
```

### Model Extensions

#### MenuItem Model Extension
Add methods to calculate total inventory across all allocated slots:
```php
public function inventoryAllocations(): HasMany
public function getTotalInventory(): int
public function getAllocatedSlots(): Collection
```

#### Branch Model Extension
Add methods for inventory management:
```php
public function trays(): HasMany
public function getTotalInventoryCapacity(): int
public function getCurrentInventoryUtilization(): float
```

## Error Handling

### Validation Rules

1. **Tray Configuration**
   - Branch must exist and be accessible by user
   - Tray name must be unique within branch
   - Cannot delete tray with occupied slots

2. **Slot Configuration**
   - Capacity must be positive integer
   - Slot name must be unique within tray
   - Cannot delete slot with inventory allocation

3. **Inventory Allocation**
   - Slot must be empty before new allocation
   - Menu item must exist and be available
   - Quantity cannot exceed slot capacity
   - Menu item must have at least one slot allocation

### Exception Handling

- **SlotOccupiedException**: Thrown when trying to allocate to occupied slot
- **InsufficientCapacityException**: Thrown when quantity exceeds capacity
- **InvalidAllocationException**: Thrown when allocation violates business rules
- **ConfigurationChangeException**: Thrown when configuration changes would cause data loss

## Testing Strategy

### Unit Tests

1. **Model Tests**
   - Test model relationships and constraints
   - Test business logic methods (capacity calculations, validation)
   - Test data integrity constraints

2. **Service Tests**
   - Test inventory allocation logic
   - Test capacity validation
   - Test configuration change scenarios

### Integration Tests

1. **Livewire Component Tests**
   - Test component interactions and state management
   - Test form validation and submission
   - Test real-time updates

2. **Database Tests**
   - Test foreign key constraints
   - Test cascade deletions
   - Test data migration scenarios

### Feature Tests

1. **End-to-End Workflows**
   - Complete branch configuration setup
   - Menu item allocation and inventory management
   - Configuration changes with existing data

2. **Permission Tests**
   - Branch manager access controls
   - Cross-branch data isolation
   - Role-based functionality access

### Performance Tests

1. **Load Testing**
   - Large number of trays and slots
   - Bulk inventory operations
   - Concurrent access scenarios

2. **Query Optimization**
   - Inventory calculation queries
   - Dashboard data loading
   - Search and filtering operations