<?php

if (!function_exists('inventory_can')) {
    /**
     * Check if the current user has a specific inventory permission
     *
     * @param string $permission
     * @return bool
     */
    function inventory_can($permission)
    {
        $user = auth()->user();
        if (!$user) return false;
        
        $service = app(\App\Services\InventoryPermissionService::class);
        
        switch ($permission) {
            case 'view_dashboard':
                return $service->canViewDashboard($user);
            case 'configure_inventory':
                return $service->canConfigureInventory($user);
            case 'manage_allocation':
                return $service->canManageAllocation($user);
            case 'create_tray':
                return $service->canCreateTray($user);
            case 'create_slot':
                return $service->canCreateSlot($user);
            case 'allocate_inventory':
                return $service->canAllocateInventory($user);
            case 'adjust_inventory':
                return $service->canAdjustInventory($user);
            default:
                return false;
        }
    }
}