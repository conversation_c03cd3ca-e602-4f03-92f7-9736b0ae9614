<?php

namespace App\Livewire;

use Livewire\Component;

class SidebarMenuItem extends Component
{

    public $name;
    public $link;
    public $icon;
    public $active = false;

    public function mount()
    {
        $this->icon = $this->setIcon($this->icon);
    }

    public function setIcon($icon)
    {
        switch ($icon) {
        case 'menu':
            $this->icon = '<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-200  dark:group-hover:text-white" height="200px" width="200px" version="1.1" id="_x32_" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" xml:space="preserve" fill="currentColor"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <style type="text/css"> .st0{fill:currentColor;} </style> <g> <path class="st0" d="M257.783,144.629v60.21c0,3.854-3.271,6.959-7.308,6.959h-1.948c-4.036,0-7.29-3.105-7.29-6.959V144.35 c0-9.916-7.011-12.882-13.708-12.882c-6.715,0-13.709,2.966-13.709,12.882v60.489c0,3.854-3.288,6.959-7.306,6.959h-1.948 c-4.019,0-7.307-3.105-7.307-6.959v-60.21c0-17.763-26.53-17.162-26.53,0.2c0,20.79,0,57.497,0,57.497 c-0.121,31.924,7.863,40.222,21.068,50.164c10.647,8.012,19.746,12.605,19.746,32.498v127.998h31.975V284.988 c0-19.893,9.081-24.486,19.728-32.498c13.205-9.942,21.19-18.24,21.068-50.164c0,0,0-36.708,0-57.497 C284.314,127.467,257.783,126.866,257.783,144.629z"></path> <path class="st0" d="M344.68,150.622c-6.802,18.172-19.536,62.568-19.536,85.115c-1.775,54.235,27.452,25.165,28.183,67.639 v109.966h31.819l0.157,0.392c0,0,0-0.166,0-0.392c0-5.106,0-65.943,0-128.006c0-61.393,0-123.926,0-134.713 C385.303,128.467,355.241,122.361,344.68,150.622z"></path> <path class="st0" d="M475.332,35.481c-4.419-10.448-11.778-19.285-21.05-25.548c-4.627-3.132-9.742-5.61-15.222-7.315 C433.579,0.913,427.768,0,421.766,0H117.111h-4.888h-21.99c-8.002,0-15.692,1.626-22.651,4.567 C57.126,9.002,48.289,16.344,42.026,25.608c-3.132,4.636-5.62,9.751-7.324,15.23c-1.705,5.463-2.609,11.282-2.609,17.258v395.807 c0,7.976,1.618,15.657,4.575,22.615c4.419,10.448,11.778,19.285,21.034,25.548c4.645,3.131,9.776,5.618,15.239,7.315 C78.42,511.087,84.231,512,90.233,512h21.99h4.888h304.655c7.985,0,15.675-1.626,22.633-4.567 c10.456-4.428,19.311-11.769,25.556-21.042c3.131-4.627,5.637-9.751,7.324-15.222c1.723-5.463,2.628-11.282,2.628-17.266V58.096 C479.907,50.129,478.272,42.439,475.332,35.481z M108.186,480.998H90.233c-3.81-0.008-7.341-0.756-10.577-2.122 c-4.837-2.053-9.012-5.506-11.952-9.847c-1.461-2.166-2.61-4.532-3.392-7.072c-0.783-2.531-1.218-5.228-1.218-8.054V58.096 c0-3.775,0.766-7.298,2.122-10.534c2.053-4.828,5.498-9.002,9.847-11.934c2.174-1.462,4.541-2.619,7.08-3.41 c2.54-0.783,5.237-1.21,8.09-1.218h17.954V480.998z M448.888,453.904c0,3.775-0.747,7.298-2.105,10.534 c-2.053,4.836-5.515,9.002-9.847,11.943c-2.174,1.462-4.558,2.61-7.08,3.401c-2.557,0.783-5.236,1.21-8.089,1.218H126.036V31.001 h295.731c3.792,0.009,7.324,0.766,10.56,2.132c4.853,2.044,9.028,5.506,11.952,9.838c1.461,2.166,2.627,4.541,3.41,7.071 c0.783,2.54,1.2,5.22,1.2,8.055V453.904z"></path> </g> </g></svg>';
                break;

        case 'pos':
            $this->icon = '<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="currentColor" viewBox="0 -0.5 25 25" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" d="M16,6 L20,6 C21.1045695,6 22,6.8954305 22,8 L22,16 C22,17.1045695 21.1045695,18 20,18 L16,18 L16,19.9411765 C16,21.0658573 15.1177541,22 14,22 L4,22 C2.88224586,22 2,21.0658573 2,19.9411765 L2,4.05882353 C2,2.93414267 2.88224586,2 4,2 L14,2 C15.1177541,2 16,2.93414267 16,4.05882353 L16,6 Z M20,11 L16,11 L16,16 L20,16 L20,11 Z M14,19.9411765 L14,4.05882353 C14,4.01396021 13.9868154,4 14,4 L4,4 C4.01318464,4 4,4.01396021 4,4.05882353 L4,19.9411765 C4,19.9860398 4.01318464,20 4,20 L14,20 C13.9868154,20 14,19.9860398 14,19.9411765 Z M5,19 L5,17 L7,17 L7,19 L5,19 Z M8,19 L8,17 L10,17 L10,19 L8,19 Z M11,19 L11,17 L13,17 L13,19 L11,19 Z M5,16 L5,14 L7,14 L7,16 L5,16 Z M8,16 L8,14 L10,14 L10,16 L8,16 Z M11,16 L11,14 L13,14 L13,16 L11,16 Z M13,5 L13,13 L5,13 L5,5 L13,5 Z M7,7 L7,11 L11,11 L11,7 L7,7 Z M20,9 L20,8 L16,8 L16,9 L20,9 Z"></path> </g></svg>';
                break;

        case 'orders':
            $this->icon = '<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="currentColor" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512.005 512.005" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <rect y="389.705" width="512.005" height="66.607"></rect> </g> </g> <g> <g> <path d="M297.643,131.433c4.862-7.641,7.693-16.696,7.693-26.404c0-27.204-22.132-49.336-49.336-49.336 c-27.204,0-49.336,22.132-49.336,49.337c0,9.708,2.831,18.763,7.693,26.404C102.739,149.772,15.208,240.563,1.801,353.747h508.398 C496.792,240.563,409.261,149.772,297.643,131.433z M256,118.415c-7.38,0-13.384-6.005-13.384-13.385S248.62,91.646,256,91.646 s13.384,6.004,13.384,13.384S263.38,118.415,256,118.415z"></path> </g> </g> </g></svg>';
                break;

        case 'customers':
            $this->icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" viewBox="0 0 16 16">
            <path d="M15 14s1 0 1-1-1-4-5-4-5 3-5 4 1 1 1 1zm-7.978-1L7 12.996c.001-.264.167-1.03.76-1.72C8.312 10.629 9.282 10 11 10c1.717 0 2.687.63 3.24 1.276.593.69.758 1.457.76 1.72l-.008.002-.014.002zM11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4m3-2a3 3 0 1 1-6 0 3 3 0 0 1 6 0M6.936 9.28a6 6 0 0 0-1.23-.247A7 7 0 0 0 5 9c-4 0-5 3-5 4q0 1 1 1h4.216A2.24 2.24 0 0 1 5 13c0-1.01.377-2.042 1.09-2.904.243-.294.526-.569.846-.816M4.92 10A5.5 5.5 0 0 0 4 13H1c0-.26.164-1.03.76-1.724.545-.636 1.492-1.256 3.16-1.275ZM1.5 5.5a3 3 0 1 1 6 0 3 3 0 0 1-6 0m3-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4"/>
            </svg>';
                break;

        case 'dashboard':
            $this->icon = '<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" viewBox="0 -0.5 25 25" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M9.35 19.0001C9.35 19.4143 9.68579 19.7501 10.1 19.7501C10.5142 19.7501 10.85 19.4143 10.85 19.0001H9.35ZM10.1 16.7691L9.35055 16.7404C9.35018 16.75 9.35 16.7595 9.35 16.7691H10.1ZM12.5 14.5391L12.4736 15.2886C12.4912 15.2892 12.5088 15.2892 12.5264 15.2886L12.5 14.5391ZM14.9 16.7691H15.65C15.65 16.7595 15.6498 16.75 15.6495 16.7404L14.9 16.7691ZM14.15 19.0001C14.15 19.4143 14.4858 19.7501 14.9 19.7501C15.3142 19.7501 15.65 19.4143 15.65 19.0001H14.15ZM10.1 18.2501C9.68579 18.2501 9.35 18.5859 9.35 19.0001C9.35 19.4143 9.68579 19.7501 10.1 19.7501V18.2501ZM14.9 19.7501C15.3142 19.7501 15.65 19.4143 15.65 19.0001C15.65 18.5859 15.3142 18.2501 14.9 18.2501V19.7501ZM10.1 19.7501C10.5142 19.7501 10.85 19.4143 10.85 19.0001C10.85 18.5859 10.5142 18.2501 10.1 18.2501V19.7501ZM9.5 19.0001V18.2501C9.4912 18.2501 9.4824 18.2502 9.4736 18.2505L9.5 19.0001ZM5.9 15.6541H5.15C5.15 15.6635 5.15018 15.673 5.15054 15.6825L5.9 15.6541ZM6.65 8.94807C6.65 8.53386 6.31421 8.19807 5.9 8.19807C5.48579 8.19807 5.15 8.53386 5.15 8.94807H6.65ZM3.0788 9.95652C2.73607 10.1891 2.64682 10.6555 2.87944 10.9983C3.11207 11.341 3.57848 11.4302 3.9212 11.1976L3.0788 9.95652ZM6.3212 9.56863C6.66393 9.336 6.75318 8.86959 6.52056 8.52687C6.28793 8.18415 5.82152 8.09489 5.4788 8.32752L6.3212 9.56863ZM5.47883 8.3275C5.13609 8.5601 5.04682 9.02651 5.27942 9.36924C5.51203 9.71198 5.97844 9.80125 6.32117 9.56865L5.47883 8.3275ZM11.116 5.40807L10.7091 4.77804C10.7043 4.78114 10.6995 4.78429 10.6948 4.7875L11.116 5.40807ZM13.884 5.40807L14.3052 4.7875C14.3005 4.78429 14.2957 4.78114 14.2909 4.77804L13.884 5.40807ZM18.6788 9.56865C19.0216 9.80125 19.488 9.71198 19.7206 9.36924C19.9532 9.02651 19.8639 8.5601 19.5212 8.3275L18.6788 9.56865ZM14.9 18.2501C14.4858 18.2501 14.15 18.5859 14.15 19.0001C14.15 19.4143 14.4858 19.7501 14.9 19.7501V18.2501ZM15.5 19.0001L15.5264 18.2505C15.5176 18.2502 15.5088 18.2501 15.5 18.2501V19.0001ZM19.1 15.6541L19.8495 15.6825C19.8498 15.673 19.85 15.6635 19.85 15.6541L19.1 15.6541ZM19.85 8.94807C19.85 8.53386 19.5142 8.19807 19.1 8.19807C18.6858 8.19807 18.35 8.53386 18.35 8.94807H19.85ZM21.079 11.1967C21.4218 11.4293 21.8882 11.3399 22.1207 10.9971C22.3532 10.6543 22.2638 10.1879 21.921 9.9554L21.079 11.1967ZM19.521 8.3274C19.1782 8.09487 18.7119 8.18426 18.4793 8.52705C18.2468 8.86984 18.3362 9.33622 18.679 9.56875L19.521 8.3274ZM10.85 19.0001V16.7691H9.35V19.0001H10.85ZM10.8495 16.7977C10.8825 15.9331 11.6089 15.2581 12.4736 15.2886L12.5264 13.7895C10.8355 13.73 9.41513 15.0497 9.35055 16.7404L10.8495 16.7977ZM12.5264 15.2886C13.3911 15.2581 14.1175 15.9331 14.1505 16.7977L15.6495 16.7404C15.5849 15.0497 14.1645 13.73 12.4736 13.7895L12.5264 15.2886ZM14.15 16.7691V19.0001H15.65V16.7691H14.15ZM10.1 19.7501H14.9V18.2501H10.1V19.7501ZM10.1 18.2501H9.5V19.7501H10.1V18.2501ZM9.4736 18.2505C7.96966 18.3035 6.70648 17.1294 6.64946 15.6257L5.15054 15.6825C5.23888 18.0125 7.19612 19.8317 9.5264 19.7496L9.4736 18.2505ZM6.65 15.6541V8.94807H5.15V15.6541H6.65ZM3.9212 11.1976L6.3212 9.56863L5.4788 8.32752L3.0788 9.95652L3.9212 11.1976ZM6.32117 9.56865L11.5372 6.02865L10.6948 4.7875L5.47883 8.3275L6.32117 9.56865ZM11.5229 6.0381C12.1177 5.65397 12.8823 5.65397 13.4771 6.0381L14.2909 4.77804C13.2008 4.07399 11.7992 4.07399 10.7091 4.77804L11.5229 6.0381ZM13.4628 6.02865L18.6788 9.56865L19.5212 8.3275L14.3052 4.7875L13.4628 6.02865ZM14.9 19.7501H15.5V18.2501H14.9V19.7501ZM15.4736 19.7496C17.8039 19.8317 19.7611 18.0125 19.8495 15.6825L18.3505 15.6257C18.2935 17.1294 17.0303 18.3035 15.5264 18.2505L15.4736 19.7496ZM19.85 15.6541V8.94807H18.35V15.6541H19.85ZM21.921 9.9554L19.521 8.3274L18.679 9.56875L21.079 11.1967L21.921 9.9554Z" fill="currentColor"></path> </g></svg>';
                break;

        case 'staff':
            $this->icon = '<svg fill="currentColor" class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512.003 512.003" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <g> <path d="M256.001,238.426c65.738,0,119.219-53.48,119.219-119.219C375.221,53.475,321.739,0,256.001,0 S136.782,53.475,136.782,119.207C136.782,184.946,190.263,238.426,256.001,238.426z M256.001,38.705 c44.397,0,80.516,36.114,80.516,80.503c0,44.397-36.119,80.516-80.516,80.516s-80.516-36.119-80.516-80.516 C175.485,74.819,211.606,38.705,256.001,38.705z"></path> <path d="M256.001,253.692c-97.97,0-177.583,79.741-177.583,177.583v61.376c0,10.687,8.664,19.352,19.352,19.352h316.462 c10.687,0,19.352-8.664,19.352-19.352v-61.377C433.584,333.215,353.747,253.692,256.001,253.692z M185.045,473.298h-67.923 c0-38.412-8.95-115.482,67.923-161.362V473.298z M288.254,473.299h-64.506v-24.156h64.506V473.299z M288.254,410.44h-64.506 V296.184c4.229-1.01,8.533-1.823,12.901-2.434v13.23c0,10.687,8.664,19.352,19.352,19.352s19.352-8.664,19.352-19.352v-13.23 c4.368,0.612,8.672,1.424,12.901,2.434V410.44z M394.881,473.298h-67.923V311.935 C403.476,357.604,394.881,434.069,394.881,473.298z"></path> </g> </g> </g> </g></svg>';
                break;

        case 'payments':
            $this->icon = '<svg width="16" height="16" viewBox="0 0 64 64" class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white"  xmlns="http://www.w3.org/2000/svg" stroke-width="3" stroke="currentColor" fill="none"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M53.36,41.21V52.14a2,2,0,0,1-2,2h-43a2,2,0,0,1-2-2v-31a2,2,0,0,1,2-2h43a2,2,0,0,1,2,2v8.93" stroke-linecap="round"></path><rect x="39.64" y="30.07" width="18" height="11.14" rx=".99" stroke-linecap="round"></rect><path d="M8.14,19.14l33.15-9.2h0a1.7,1.7,0,0,1,2.15,1.6l.27,7" stroke-linecap="round"></path><line x1="45.73" y1="35.64" x2="46.26" y2="35.64" stroke-linecap="round"></line></g></svg>';
                break;

        case 'settings':
            $this->icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white bi bi-gear" viewBox="0 0 16 16">
  <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492M5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0"/>
  <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115z"/>
</svg>';
                break;

        case 'reservations':
            $this->icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" viewBox="0 0 16 16"><path d="M10.854 7.146a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7.5 9.793l2.646-2.647a.5.5 0 0 1 .708 0"/><path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5M1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4z"/></svg>';
                break;

        case 'restaurants':
            $this->icon = '<svg width="16" height="16" fill="currentColor" class="w-5 h-5 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="#000000" class="bi bi-shop"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M2.97 1.35A1 1 0 0 1 3.73 1h8.54a1 1 0 0 1 .76.35l2.609 3.044A1.5 1.5 0 0 1 16 5.37v.255a2.375 2.375 0 0 1-4.25 1.458A2.371 2.371 0 0 1 9.875 8 2.37 2.37 0 0 1 8 7.083 2.37 2.37 0 0 1 6.125 8a2.37 2.37 0 0 1-1.875-.917A2.375 2.375 0 0 1 0 5.625V5.37a1.5 1.5 0 0 1 .361-.976l2.61-3.045zm1.78 4.275a1.375 1.375 0 0 0 2.75 0 .5.5 0 0 1 1 0 1.375 1.375 0 0 0 2.75 0 .5.5 0 0 1 1 0 1.375 1.375 0 1 0 2.75 0V5.37a.5.5 0 0 0-.12-.325L12.27 2H3.73L1.12 5.045A.5.5 0 0 0 1 5.37v.255a1.375 1.375 0 0 0 2.75 0 .5.5 0 0 1 1 0zM1.5 8.5A.5.5 0 0 1 2 9v6h1v-5a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v5h6V9a.5.5 0 0 1 1 0v6h.5a.5.5 0 0 1 0 1H.5a.5.5 0 0 1 0-1H1V9a.5.5 0 0 1 .5-.5zM4 15h3v-5H4v5zm5-5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-3zm3 0h-2v3h2v-3z"></path> </g></svg>';
                break;

        case 'packages' :
            $this->icon = '<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="currentColor"  version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 491 491" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M489.658,189.942l-45.205-85.169c-1.534-2.889-4.147-4.877-7.108-5.693c-0.02-0.005-0.032-0.012-0.053-0.018L248.97,48.229 c-1.958-0.527-4.021-0.53-5.98-0.004L53.721,99.058c-0.021,0.006-0.035,0.013-0.057,0.019c-2.966,0.814-5.583,2.804-7.118,5.696 L1.342,189.942c-1.473,2.776-1.746,6.034-0.756,9.016c0.99,2.982,3.158,5.43,5.999,6.772l39.331,18.592v116.344 c0,4.414,2.526,8.439,6.502,10.356l188.557,91.002c1.584,0.765,3.293,1.143,4.997,1.143c1.691,0,3.372-0.389,4.927-1.127 c0.025-0.006,0.049-0.005,0.072-0.017l188.557-91.002c3.976-1.918,6.502-5.942,6.502-10.356V224.332c0-0.15-0.017-0.297-0.022-0.445 l38.408-18.156c2.841-1.343,5.009-3.79,5.999-6.772C491.404,195.975,491.131,192.719,489.658,189.942z M245.965,71.241 l150.587,40.647L245.5,167.87L94.52,111.916L245.965,71.241z M62.144,124.445l167.892,62.223l-36.977,81.771L27.306,190.085 L62.144,124.445z M68.916,333.447v-98.252l124.887,59.035c1.552,0.734,3.232,1.104,4.915,1.104c1.341,0,2.685-0.234,3.968-0.706 c2.895-1.064,5.24-3.246,6.511-6.056l25.276-55.896v180.673L68.916,333.447z M423.029,333.447l-165.557,79.902V234.767 l24.331,53.805c1.271,2.81,3.616,4.991,6.511,6.056c1.283,0.472,2.627,0.706,3.968,0.706c1.684,0,3.363-0.369,4.915-1.104 l125.832-59.482V333.447z M297.941,268.438l-36.978-81.771l167.892-62.223l34.839,65.64L297.941,268.438z"></path> </g></svg>';
            break;
        case 'billing' :
            $this->icon = '<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="currentColor" style="opacity:.8" width="24" height="24" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h48v48H0z" fill="none"/><path d="m6 44 9-4 9 4 9-4 9 4V4H6zm4-36h28v29.845l-3.375-1.5L33 35.623l-1.625.722L24 39.623l-7.375-3.278L15 35.623l-1.625.722-3.375 1.5z"/><path d="M14 12h20v4H14zm0 8h20v4H14zm0 8h12v4H14z"/></svg>';
            break;
        case 'offline-plan-request':
            $this->icon ='<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="m8.38 12 2.41 2.42 4.83-4.84" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M10.75 2.45c.69-.59 1.82-.59 2.52 0l1.58 1.36c.3.26.86.47 1.26.47h1.7c1.06 0 1.93.87 1.93 1.93v1.7c0 .39.21.96.47 1.26l1.36 1.58c.59.69.59 1.82 0 2.52l-1.36 1.58c-.26.3-.47.86-.47 1.26v1.7c0 1.06-.87 1.93-1.93 1.93h-1.7c-.39 0-.96.21-1.26.47l-1.58 1.36c-.69.59-1.82.59-2.52 0l-1.58-1.36c-.3-.26-.86-.47-1.26-.47H6.18c-1.06 0-1.93-.87-1.93-1.93V16.1c0-.39-.21-.95-.46-1.25l-1.35-1.59c-.58-.69-.58-1.81 0-2.5l1.35-1.59c.25-.3.46-.86.46-1.25V6.2c0-1.06.87-1.93 1.93-1.93h1.73c.39 0 .96-.21 1.26-.47z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>';
            break;

        case 'landing' :
            $this->icon = '<svg class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="currentColor" style="opacity:.9" width="24" height="24" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"><g stroke-width="0"/><g stroke-linecap="round" stroke-linejoin="round"/><g data-name="Layer 2"><path fill="none" data-name="invisible box" d="M0 0h48v48H0z"/><g data-name="icons Q2"><path d="M7 34h12a2 2 0 0 0 0-4H9V10h30v20H29a2 2 0 0 0 0 4h12a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v24a2 2 0 0 0 2 2m37 4H4a2 2 0 0 0 0 4h40a2 2 0 0 0 0-4"/><path d="M31.9 21.3A5.7 5.7 0 0 0 32 20a5.7 5.7 0 0 0-.1-1.3l-2.2-.5a4.2 4.2 0 0 0-.4-1l1.2-1.9a7.7 7.7 0 0 0-1.8-1.8l-1.9 1.2-1-.4-.5-2.2h-2.6l-.5 2.2-1 .4-1.9-1.2a7.7 7.7 0 0 0-1.8 1.8l1.2 1.9a4.2 4.2 0 0 0-.4 1l-2.2.5A5.7 5.7 0 0 0 16 20a5.7 5.7 0 0 0 .1 1.3l2.2.5a4.2 4.2 0 0 0 .4 1l-1.2 1.9a7.7 7.7 0 0 0 1.8 1.8l1.9-1.2 1 .4.5 2.2h2.6l.5-2.2 1-.4 1.9 1.2a7.7 7.7 0 0 0 1.8-1.8l-1.2-1.9a4.2 4.2 0 0 0 .4-1ZM24 22a2 2 0 1 1 2-2 2 2 0 0 1-2 2"/></g></g></svg>';
            break;

        case 'waiterRequest' :
            $this->icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" viewBox="0 0 16 16">
  <path d="M8 16a2 2 0 0 0 2-2H6a2 2 0 0 0 2 2M8 1.918l-.797.161A4 4 0 0 0 4 6c0 .628-.134 2.197-.459 3.742-.16.767-.376 1.566-.663 2.258h10.244c-.287-.692-.502-1.49-.663-2.258C12.134 8.197 12 6.628 12 6a4 4 0 0 0-3.203-3.92zM14.22 12c.223.447.481.801.78 1H1c.299-.199.557-.553.78-1C2.68 10.2 3 6.88 3 6c0-2.42 1.72-4.44 4.005-4.901a1 1 0 1 1 1.99 0A5 5 0 0 1 13 6c0 .88.32 4.2 1.22 6"/>
</svg>';
            break;

        case 'delivery' :
            $this->icon = '<svg version="1.0" xmlns="http://www.w3.org/2000/svg"
 viewBox="0 0 512.000000 512.000000" fill="currentColor"
 preserveAspectRatio="xMidYMid meet" class="w-6 h-6 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white">

<g transform="translate(0.000000,512.000000) scale(0.100000,-0.100000)" stroke="none">
<path d="M2605 4790 c-66 -13 -155 -48 -213 -82 -71 -42 -178 -149 -220 -221
-145 -242 -112 -552 79 -761 59 -64 61 -67 38 -73 -13 -4 -60 -24 -104 -46
-151 -75 -295 -249 -381 -462 -20 -49 -38 -91 -39 -93 -2 -2 -19 8 -40 22 -21
14 -54 30 -74 36 -59 16 -947 12 -994 -4 -120 -43 -181 -143 -122 -201 32 -33
76 -33 106 0 41 44 72 55 159 55 l80 0 0 -135 c0 -131 1 -137 25 -160 l24 -25
231 0 231 0 24 25 c24 23 25 29 25 161 l0 136 95 -4 c82 -3 97 -6 117 -26 l23
-23 0 -349 0 -349 -23 -23 -23 -23 -465 -3 -465 -3 -29 30 c-17 16 -30 34 -30
40 0 7 34 11 95 11 88 0 98 2 120 25 16 15 25 36 25 55 0 19 -9 40 -25 55 -22
23 -32 25 -120 25 l-95 0 0 80 0 80 55 0 c67 0 105 29 105 80 0 19 -9 40 -25
55 l-24 25 -231 0 -231 0 -24 -25 c-33 -32 -33 -78 0 -110 22 -23 32 -25 120
-25 l95 0 0 -80 0 -80 -175 0 c-173 0 -176 0 -200 -25 -33 -32 -33 -78 0 -110
24 -25 27 -25 197 -25 l174 0 12 -45 c23 -88 85 -154 171 -183 22 -8 112 -12
253 -12 l220 0 -37 -43 c-103 -119 -197 -418 -211 -669 -7 -115 -7 -116 19
-142 26 -25 29 -26 164 -26 l138 0 16 -69 c55 -226 235 -407 464 -466 77 -20
233 -20 310 0 228 59 409 240 463 464 l17 71 605 0 606 0 13 -62 c58 -281 328
-498 621 -498 349 0 640 291 640 640 0 237 -141 465 -350 569 -89 43 -193 71
-271 71 l-46 0 -142 331 c-78 183 -140 333 -139 335 2 1 28 -4 58 -12 80 -21
117 -18 145 11 l25 24 0 351 0 351 -26 26 c-24 24 -30 25 -91 20 -130 -12
-265 -105 -317 -217 l-23 -49 -29 30 c-16 17 -51 43 -79 57 -49 26 -54 27
-208 24 -186 -3 -227 9 -300 87 -43 46 -137 173 -137 185 0 3 10 6 23 6 13 0
48 12 78 28 61 31 112 91 131 155 7 25 25 53 45 70 79 68 91 152 34 242 -17
27 -36 65 -41 85 -13 46 -13 100 0 100 6 0 22 11 35 25 30 29 33 82 10 190
-61 290 -332 508 -630 504 -38 -1 -88 -5 -110 -9z m230 -165 c87 -23 168 -70
230 -136 55 -57 108 -153 121 -216 l6 -31 -153 -4 c-131 -3 -161 -6 -201 -25
-66 -30 -133 -96 -165 -162 -26 -52 -28 -66 -31 -210 l-4 -153 -31 6 c-63 13
-159 66 -216 121 -66 62 -113 143 -136 230 -88 339 241 668 580 580z m293
-619 c7 -41 28 -106 48 -147 l36 -74 -24 -15 c-43 -28 -68 -59 -68 -85 0 -40
-26 -92 -54 -110 -30 -20 -127 -16 -211 8 l-50 14 -3 175 c-2 166 -1 176 21
218 35 67 86 90 202 90 l91 0 12 -74z m-538 -496 c132 -25 214 -88 348 -269
101 -137 165 -199 241 -237 31 -15 57 -29 59 -30 2 -1 -6 -20 -17 -43 -12 -22
-27 -75 -33 -117 -12 -74 -12 -76 -38 -71 -149 30 -321 156 -424 311 -53 80
-90 95 -140 55 -48 -38 -35 -89 52 -204 l30 -39 -28 -36 c-42 -54 -91 -145
-110 -208 l-18 -57 -337 -3 -338 -2 6 82 c9 112 47 272 95 400 135 357 365
522 652 468z m1490 -630 c0 -254 1 -252 -83 -167 -54 53 -77 104 -77 167 0 63
23 114 77 168 84 84 83 86 83 -168z m-454 63 c18 -13 41 -46 57 -83 l26 -61
-45 -19 c-75 -33 -165 -52 -244 -54 l-75 -1 -3 29 c-8 72 44 166 113 201 42
22 132 16 171 -12z m-2346 -63 l0 -80 -120 0 -120 0 0 80 0 80 120 0 120 0 0
-80z m1584 -184 c80 -52 154 -84 261 -111 l90 -23 112 -483 c68 -295 112 -506
112 -540 1 -68 -21 -134 -56 -171 l-26 -27 -17 48 c-29 86 -99 159 -177 186
l-38 13 -6 279 c-5 297 -5 297 -64 414 -58 113 -212 233 -328 254 -21 4 -41
14 -44 21 -12 32 88 201 111 186 6 -4 37 -24 70 -46z m1099 -493 l185 -433
-174 -245 -174 -245 -138 0 -138 0 33 68 c40 81 56 176 44 252 -8 47 -203 894
-217 941 -4 13 9 17 75 23 80 6 230 44 280 71 14 7 29 10 32 7 4 -4 90 -202
192 -439z m-1323 187 c118 -22 229 -99 275 -190 37 -74 45 -138 45 -375 l0
-225 -160 0 -160 0 0 115 c0 179 -47 289 -158 369 -91 67 -141 76 -417 76
l-244 0 10 32 c5 18 9 72 9 120 l0 88 374 0 c209 0 397 -4 426 -10z m-319
-402 c50 -15 111 -67 135 -115 16 -32 20 -70 24 -244 l5 -205 36 -72 35 -72
-759 0 -759 0 7 63 c17 164 95 400 165 502 47 68 129 124 215 145 52 13 853
12 896 -2z m2114 -323 c256 -67 415 -329 350 -580 -48 -184 -202 -326 -390
-358 -197 -34 -412 76 -500 257 -19 39 -38 86 -41 104 l-6 32 80 0 81 0 24
-53 c31 -69 86 -123 156 -156 77 -36 192 -36 266 -1 63 31 124 91 156 155 33
68 34 197 2 267 -27 60 -95 127 -156 157 -95 46 -229 36 -311 -22 -18 -12 -26
-15 -21 -6 13 22 126 182 143 202 19 22 86 23 167 2z m-1315 -243 c39 -21 87
-99 77 -125 -6 -15 -27 -17 -178 -17 -193 0 -231 7 -289 58 -35 29 -70 78 -70
97 0 3 96 5 213 5 187 0 217 -2 247 -18z m1288 -89 c51 -38 67 -70 67 -133 0
-63 -16 -95 -69 -134 -43 -33 -132 -29 -179 7 -20 15 -37 32 -37 38 0 5 36 9
80 9 73 0 83 3 105 25 33 32 33 78 0 110 -22 22 -32 25 -105 25 -44 0 -80 4
-80 8 0 12 29 37 65 57 39 21 117 15 153 -12z m-397 -46 c-10 -9 -11 -8 -5 6
3 10 9 15 12 12 3 -3 0 -11 -7 -18z m-2460 -217 c45 -106 169 -184 289 -184
120 0 244 78 289 184 l22 50 81 0 81 0 -7 -32 c-13 -65 -66 -159 -123 -219
-186 -195 -500 -195 -686 0 -57 60 -110 154 -123 219 l-6 32 80 0 81 0 22 -50z
m419 41 c0 -16 -51 -50 -91 -63 -30 -8 -48 -8 -78 0 -40 13 -91 47 -91 63 0 5
57 9 130 9 73 0 130 -4 130 -9z"/>
</g>
</svg>';
            break;

        default:
            $this->icon = '<svg class="w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" viewBox="0 -0.5 25 25" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M9.41728 18.9999C9.41728 19.4142 9.75307 19.7499 10.1673 19.7499C10.5815 19.7499 10.9173 19.4142 10.9173 18.9999H9.41728ZM10.1673 16.6669H9.41728H10.1673ZM14.0853 18.9999C14.0853 19.4142 14.4211 19.7499 14.8353 19.7499C15.2495 19.7499 15.5853 19.4142 15.5853 18.9999H14.0853ZM10.1673 19.7499C10.5815 19.7499 10.9173 19.4142 10.9173 18.9999C10.9173 18.5857 10.5815 18.2499 10.1673 18.2499V19.7499ZM7.83328 18.9999L7.82564 19.7499H7.83328V18.9999ZM5.80128 17.2529L5.0518 17.2807C5.05294 17.3116 5.056 17.3424 5.06095 17.373L5.80128 17.2529ZM5.53228 9.99395L6.28177 9.96617C6.2805 9.93188 6.27687 9.8977 6.27092 9.8639L5.53228 9.99395ZM6.64428 7.74195L6.3033 7.07392L6.29848 7.07642L6.64428 7.74195ZM11.5793 5.22295L11.9203 5.89096L11.9218 5.89017L11.5793 5.22295ZM13.4243 5.22295L13.0818 5.89017L13.0833 5.89096L13.4243 5.22295ZM18.3593 7.74195L18.7051 7.07641L18.7003 7.07394L18.3593 7.74195ZM19.4713 9.99395L18.7326 9.8639C18.7267 9.89767 18.7231 9.93181 18.7218 9.96607L19.4713 9.99395ZM19.2013 17.2529L19.9416 17.373C19.9466 17.3425 19.9496 17.3117 19.9508 17.2808L19.2013 17.2529ZM17.1693 18.9999V19.75L17.1769 19.7499L17.1693 18.9999ZM14.8353 18.2499C14.4211 18.2499 14.0853 18.5857 14.0853 18.9999C14.0853 19.4142 14.4211 19.7499 14.8353 19.7499V18.2499ZM10.1673 18.2499C9.75307 18.2499 9.41728 18.5857 9.41728 18.9999C9.41728 19.4142 9.75307 19.7499 10.1673 19.7499V18.2499ZM14.8353 19.7499C15.2495 19.7499 15.5853 19.4142 15.5853 18.9999C15.5853 18.5857 15.2495 18.2499 14.8353 18.2499V19.7499ZM10.9173 18.9999V16.6669H9.41728V18.9999H10.9173ZM10.9173 16.6669C10.9173 15.7921 11.6265 15.0829 12.5013 15.0829V13.5829C10.798 13.5829 9.41728 14.9637 9.41728 16.6669H10.9173ZM12.5013 15.0829C13.3761 15.0829 14.0853 15.7921 14.0853 16.6669H15.5853C15.5853 14.9637 14.2045 13.5829 12.5013 13.5829V15.0829ZM14.0853 16.6669V18.9999H15.5853V16.6669H14.0853ZM10.1673 18.2499H7.83328V19.7499H10.1673V18.2499ZM7.84092 18.25C7.1937 18.2434 6.64521 17.7718 6.54162 17.1329L5.06095 17.373C5.28137 18.7325 6.44847 19.7359 7.82564 19.7499L7.84092 18.25ZM6.55077 17.2252L6.28177 9.96617L4.7828 10.0217L5.0518 17.2807L6.55077 17.2252ZM6.27092 9.8639C6.16697 9.27348 6.45811 8.68388 6.99008 8.40747L6.29848 7.07642C5.18533 7.65481 4.57613 8.88855 4.79364 10.124L6.27092 9.8639ZM6.98526 8.40996L11.9203 5.89096L11.2383 4.55494L6.30331 7.07394L6.98526 8.40996ZM11.9218 5.89017C12.2859 5.70328 12.7177 5.70328 13.0818 5.89017L13.7668 4.55573C12.9727 4.14809 12.0309 4.14809 11.2368 4.55573L11.9218 5.89017ZM13.0833 5.89096L18.0183 8.40996L18.7003 7.07394L13.7653 4.55494L13.0833 5.89096ZM18.0135 8.40747C18.5455 8.68388 18.8366 9.27348 18.7326 9.8639L20.2099 10.124C20.4274 8.88855 19.8182 7.65481 18.7051 7.07642L18.0135 8.40747ZM18.7218 9.96607L18.4518 17.2251L19.9508 17.2808L20.2208 10.0218L18.7218 9.96607ZM18.461 17.1329C18.3574 17.7718 17.8089 18.2434 17.1616 18.25L17.1769 19.7499C18.5541 19.7359 19.7212 18.7325 19.9416 17.373L18.461 17.1329ZM17.1693 18.2499H14.8353V19.7499H17.1693V18.2499ZM10.1673 19.7499H14.8353V18.2499H10.1673V19.7499Z" fill="currentColor"></path> </g></svg>';
                break;
        }

        return $this->icon;
    }

    public function render()
    {
        return view('livewire.sidebar-menu-item');
    }

}
