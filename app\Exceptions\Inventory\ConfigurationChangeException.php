<?php

namespace App\Exceptions\Inventory;

use Exception;

class ConfigurationChangeException extends Exception
{
    protected $changeType;
    protected $affectedData;
    protected $requiresConfirmation;

    public const CHANGE_REDUCE_TRAYS = 'reduce_trays';
    public const CHANGE_REDUCE_SLOTS = 'reduce_slots';
    public const CHANGE_REDUCE_CAPACITY = 'reduce_capacity';
    public const CHANGE_DELETE_TRAY = 'delete_tray';
    public const CHANGE_DELETE_SLOT = 'delete_slot';

    public function __construct(
        string $changeType,
        array $affectedData = [],
        bool $requiresConfirmation = true,
        string $message = null
    ) {
        $this->changeType = $changeType;
        $this->affectedData = $affectedData;
        $this->requiresConfirmation = $requiresConfirmation;

        $message = $message ?: $this->generateMessage();
        
        parent::__construct($message);
    }

    public function getChangeType(): string
    {
        return $this->changeType;
    }

    public function getAffectedData(): array
    {
        return $this->affectedData;
    }

    public function requiresConfirmation(): bool
    {
        return $this->requiresConfirmation;
    }

    private function generateMessage(): string
    {
        switch ($this->changeType) {
            case self::CHANGE_REDUCE_TRAYS:
                $count = count($this->affectedData['trays'] ?? []);
                return "Reducing tray count will delete {$count} tray(s) and all their slots and inventory allocations.";
            case self::CHANGE_REDUCE_SLOTS:
                $count = count($this->affectedData['slots'] ?? []);
                return "Reducing slot count will delete {$count} slot(s) and their inventory allocations.";
            case self::CHANGE_REDUCE_CAPACITY:
                $slotId = $this->affectedData['slot_id'] ?? 'unknown';
                $currentInventory = $this->affectedData['current_inventory'] ?? 0;
                $newCapacity = $this->affectedData['new_capacity'] ?? 0;
                return "Reducing capacity of slot {$slotId} to {$newCapacity} will require removing {$currentInventory} items from inventory.";
            case self::CHANGE_DELETE_TRAY:
                $trayId = $this->affectedData['tray_id'] ?? 'unknown';
                $slotCount = count($this->affectedData['slots'] ?? []);
                return "Deleting tray {$trayId} will also delete {$slotCount} slot(s) and all their inventory allocations.";
            case self::CHANGE_DELETE_SLOT:
                $slotId = $this->affectedData['slot_id'] ?? 'unknown';
                $hasInventory = !empty($this->affectedData['inventory_allocation']);
                return $hasInventory 
                    ? "Deleting slot {$slotId} will remove its current inventory allocation."
                    : "Deleting slot {$slotId}.";
            default:
                return "This configuration change may result in data loss.";
        }
    }

    public function getUserFriendlyMessage(): string
    {
        switch ($this->changeType) {
            case self::CHANGE_REDUCE_TRAYS:
                return "Warning: Reducing the number of trays will permanently delete some trays and all their contents. This action cannot be undone.";
            case self::CHANGE_REDUCE_SLOTS:
                return "Warning: Reducing the number of slots will permanently delete some slots and their inventory. This action cannot be undone.";
            case self::CHANGE_REDUCE_CAPACITY:
                return "Warning: Reducing slot capacity below current inventory levels will require removing items. This action cannot be undone.";
            case self::CHANGE_DELETE_TRAY:
                return "Warning: Deleting this tray will permanently remove all its slots and inventory. This action cannot be undone.";
            case self::CHANGE_DELETE_SLOT:
                return "Warning: Deleting this slot will permanently remove its inventory allocation. This action cannot be undone.";
            default:
                return "Warning: This change may result in permanent data loss. Please confirm to proceed.";
        }
    }

    public function getConfirmationPrompt(): string
    {
        return "Are you sure you want to proceed with this change? Type 'CONFIRM' to continue.";
    }
}