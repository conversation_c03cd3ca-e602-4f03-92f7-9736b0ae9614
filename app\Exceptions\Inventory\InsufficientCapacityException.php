<?php

namespace App\Exceptions\Inventory;

use Exception;

class InsufficientCapacityException extends Exception
{
    protected $slotId;
    protected $requestedQuantity;
    protected $availableCapacity;
    protected $currentQuantity;

    public function __construct(
        int $slotId,
        int $requestedQuantity,
        int $availableCapacity,
        int $currentQuantity = 0,
        string $message = null
    ) {
        $this->slotId = $slotId;
        $this->requestedQuantity = $requestedQuantity;
        $this->availableCapacity = $availableCapacity;
        $this->currentQuantity = $currentQuantity;

        $message = $message ?: "Insufficient capacity in slot {$slotId}. Requested: {$requestedQuantity}, Available: {$availableCapacity}, Current: {$currentQuantity}.";
        
        parent::__construct($message);
    }

    public function getSlotId(): int
    {
        return $this->slotId;
    }

    public function getRequestedQuantity(): int
    {
        return $this->requestedQuantity;
    }

    public function getAvailableCapacity(): int
    {
        return $this->availableCapacity;
    }

    public function getCurrentQuantity(): int
    {
        return $this->currentQuantity;
    }

    public function getUserFriendlyMessage(): string
    {
        return "Not enough space in this slot. You can only add {$this->availableCapacity} more items (currently has {$this->currentQuantity} items).";
    }
}