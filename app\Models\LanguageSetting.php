<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;

use App\Models\BaseModel;

class LanguageSetting extends BaseModel
{
    protected $guarded = ['id'];

    const LANGUAGES_TRANS = [
        'en' => 'English',
        'ar' => 'عربي',
        'fr' => 'français',
        'zh-CN' => '中国人',
        'amh' => 'አማርኛ',   /// cant use amharic words
	'tig' => 'ትግርኛ',
	'oro' => 'afaan oromo',
    ];

    const LANGUAGES = [
	[
            'language_code' => 'amh',
            'flag_code' => 'et', // Ethiopia
            'language_name' => 'አማርኛ',
            'active' => 1,
            'is_rtl' => 0,
        ],
        [
            'language_code' => 'tig',
            'flag_code' => 'et', 
            'language_name' => 'ትግርኛ',
            'active' => 1,
            'is_rtl' => 0,
	],
	[
            'language_code' => 'oro',
            'flag_code' => 'et',
            'language_name' => 'afaan oromo',
            'active' => 1,
            'is_rtl' => 0,
        ],
        [
            'language_code' => 'en',
            'flag_code' => 'gb',
            'language_name' => 'English',
            'active' => 1,
            'is_rtl' => 0,
        ],
        [
            'language_code' => 'ar',
            'flag_code' => 'sa',
            'language_name' => 'Arabic',
            'active' => 0,
            'is_rtl' => 1,
        ],
        [
            'language_code' => 'fr',
            'flag_code' => 'fr',
            'language_name' => 'French',
            'active' => 0,
            'is_rtl' => 0,
        ],
        [
            'language_code' => 'zh-CN',
            'flag_code' => 'cn',
            'language_name' => 'Chinese (S)',
            'active' => 0,
            'is_rtl' => 0,

        ],
    ];


    public function flagUrl(): Attribute
    {
        return Attribute::get(function (): string {
            return asset('flags/1x1/' . strtolower($this->flag_code) . '.svg');
        });
    }
}
