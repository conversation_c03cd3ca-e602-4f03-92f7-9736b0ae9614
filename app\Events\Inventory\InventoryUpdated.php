<?php

namespace App\Events\Inventory;

use App\Models\InventoryAllocation;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class InventoryUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public InventoryAllocation $allocation;
    public string $updateType;
    public int $quantityChanged;
    public int $previousQuantity;

    /**
     * Create a new event instance.
     */
    public function __construct(
        InventoryAllocation $allocation, 
        string $updateType, 
        int $quantityChanged, 
        int $previousQuantity
    ) {
        $this->allocation = $allocation;
        $this->updateType = $updateType; // 'added', 'removed', 'set'
        $this->quantityChanged = $quantityChanged;
        $this->previousQuantity = $previousQuantity;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('branch.' . $this->allocation->slot->tray->branch_id . '.inventory'),
            new PrivateChannel('inventory.allocation.' . $this->allocation->id)
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'allocation_id' => $this->allocation->id,
            'slot_id' => $this->allocation->slot_id,
            'menu_item_id' => $this->allocation->menu_item_id,
            'menu_item_name' => $this->allocation->menuItem->name,
            'current_quantity' => $this->allocation->current_quantity,
            'previous_quantity' => $this->previousQuantity,
            'quantity_changed' => $this->quantityChanged,
            'update_type' => $this->updateType,
            'slot_capacity' => $this->allocation->slot->capacity,
            'remaining_capacity' => $this->allocation->getRemainingCapacity(),
            'utilization_percentage' => $this->allocation->getCapacityUtilization(),
            'is_at_capacity' => $this->allocation->isAtCapacity(),
            'is_near_capacity' => $this->allocation->isNearCapacity(),
            'is_empty' => $this->allocation->isEmpty(),
            'tray_name' => $this->allocation->slot->tray->name,
            'slot_name' => $this->allocation->slot->name,
            'timestamp' => now()->toISOString()
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'inventory.updated';
    }
}