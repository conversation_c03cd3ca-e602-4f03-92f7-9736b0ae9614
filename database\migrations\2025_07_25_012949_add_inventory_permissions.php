<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Module;
use Spatie\Permission\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the Inventory module
        $inventoryModule = Module::where('name', 'Inventory')->first();
        
        if (!$inventoryModule) {
            throw new Exception('Inventory module not found. Please run the add_inventory_module_to_modules_table migration first.');
        }

        // Define inventory permissions
        $permissions = [
            ['guard_name' => 'web', 'name' => 'View Inventory Dashboard', 'module_id' => $inventoryModule->id],
            ['guard_name' => 'web', 'name' => 'Configure Inventory', 'module_id' => $inventoryModule->id],
            ['guard_name' => 'web', 'name' => 'Manage Inventory Allocation', 'module_id' => $inventoryModule->id],
            ['guard_name' => 'web', 'name' => 'Create Tray', 'module_id' => $inventoryModule->id],
            ['guard_name' => 'web', 'name' => 'Update Tray', 'module_id' => $inventoryModule->id],
            ['guard_name' => 'web', 'name' => 'Delete Tray', 'module_id' => $inventoryModule->id],
            ['guard_name' => 'web', 'name' => 'Create Slot', 'module_id' => $inventoryModule->id],
            ['guard_name' => 'web', 'name' => 'Update Slot', 'module_id' => $inventoryModule->id],
            ['guard_name' => 'web', 'name' => 'Delete Slot', 'module_id' => $inventoryModule->id],
            ['guard_name' => 'web', 'name' => 'Allocate Inventory', 'module_id' => $inventoryModule->id],
            ['guard_name' => 'web', 'name' => 'Adjust Inventory', 'module_id' => $inventoryModule->id],
        ];

        // Insert permissions into the database
        Permission::insert($permissions);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get the Inventory module
        $inventoryModule = Module::where('name', 'Inventory')->first();
        
        if ($inventoryModule) {
            // Remove all inventory permissions
            Permission::where('module_id', $inventoryModule->id)->delete();
        }
    }
};