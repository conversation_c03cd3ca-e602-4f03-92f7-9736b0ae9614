<?php

namespace App\Livewire\Inventory;

use App\Models\Branch;
use App\Models\Tray;
use App\Models\Slot;
use App\Models\MenuItem;
use App\Models\InventoryAllocation;
use App\Services\BranchInventoryService;
use App\Services\InventoryValidationService;
use App\Services\InventoryPermissionService;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Jantinnerezo\LivewireAlert\LivewireAlert;

class InventoryAllocationManager extends Component
{
    use WithPagination, LivewireAlert;

    public Branch $branch;
    public $selectedTrayId = null;
    public $selectedSlotId = null;
    public $selectedMenuItemId = null;
    public $quantityToAdd = 1;
    public $quantityToRemove = 1;
    public $newQuantity = 0;
    public $searchTerm = '';
    public $filterStatus = 'all'; // all, occupied, available, near_capacity, at_capacity
    public $menuItemFilter = 'all'; // Add menu item filter
    public $showAllocationModal = false;
    public $showQuantityModal = false;
    public $quantityAction = 'set'; // add, remove, set
    public $currentSlot = null;
    public $errorMessage = '';
    public $successMessage = '';
    
    // Tray/Slot Management Properties
    public $showAddTrayModal = false;
    public $showEditTrayModal = false;
    public $showAddSlotModal = false;
    public $showEditSlotModal = false;
    public $showDeleteConfirmation = false;
    
    // Tray form data
    public $trayName = '';
    public $traySortOrder = '';
    public $selectedTray = null;
    
    // Slot form data
    public $slotName = '';
    public $slotCapacity = '';
    public $slotSortOrder = '';
    public $selectedSlot = null;
    public $selectedTrayForSlot = null;
    
    // Delete confirmation
    public $deleteType = '';
    public $deleteItem = null;
    public $deleteWarnings = [];

    protected BranchInventoryService $inventoryService;
    protected InventoryValidationService $validationService;

    public function boot(
        BranchInventoryService $inventoryService,
        InventoryValidationService $validationService
    ) {
        $this->inventoryService = $inventoryService;
        $this->validationService = $validationService;
    }

    public function mount(Branch $branch)
    {
    // Check permissions
//        $permissionService = app(InventoryPermissionService::class);
#        if (!$permissionService->canManageAllocation(auth()->user())) {
#            abort(403, 'Insufficient permissions to manage inventory allocation');
#        }
#
#        // Ensure user can only access their own branch
#        if (auth()->user()->branch_id !== $branch->id) {
#            abort(403, 'Access denied to this branch inventory');
#        }
#       
#       use the following to check fro admin has the same restaurant id but removed fro now 
#
#       if (auth()->user()->restaurant_id == $branch->restaurant->id){
	 
	 
	$this->branch = $branch;
    }

    public function render()
    {
        $trays = $this->getTraysWithSlots();
        $menuItems = $this->getAvailableMenuItems();
        $summary = $this->inventoryService->getBranchInventorySummary($this->branch);
        
        return view('livewire.inventory.inventory-allocation-manager', [
            'trays' => $trays,
            'menuItems' => $menuItems,
            'summaryStats' => $this->getSummaryStats(),
            'menuItemsForFilter' => $this->getMenuItemsForFilter(),
            'summary' => $summary
        ]);
    }

    /**
     * Get trays with slots filtered by search and status
     */
    protected function getTraysWithSlots()
    {
        $query = $this->branch->trays()
            ->with(['slots.inventoryAllocation.menuItem'])
            ->orderBy('sort_order');

        if ($this->selectedTrayId) {
            $query->where('id', $this->selectedTrayId);
        }

        $trays = $query->get();

        // Filter slots based on search term and status
        $trays->each(function ($tray) {
            $tray->setRelation('slots', $tray->slots->filter(function ($slot) {
                return $this->shouldIncludeSlot($slot);
            }));
        });

        return $trays;
    }

    /**
     * Check if slot should be included based on filters
     */
    protected function shouldIncludeSlot(Slot $slot): bool
    {
        // Search filter
        if ($this->searchTerm) {
            $searchMatch = false;
            
            // Search in slot name
            if (stripos($slot->name, $this->searchTerm) !== false) {
                $searchMatch = true;
            }
            
            // Search in menu item name if slot is occupied
            if ($slot->isOccupied()) {
                $menuItem = $slot->getAssignedMenuItem();
                if ($menuItem && stripos($menuItem->item_name, $this->searchTerm) !== false) {
                    $searchMatch = true;
                }
            }
            
            if (!$searchMatch) {
                return false;
            }
        }

        // Menu item filter
        if ($this->menuItemFilter !== 'all') {
            if (!$slot->inventoryAllocation || $slot->inventoryAllocation->menu_item_id != $this->menuItemFilter) {
                return false;
            }
        }

        // Status filter
        switch ($this->filterStatus) {
            case 'occupied':
                return $slot->isOccupied();
            case 'available':
                return !$slot->isOccupied();
            case 'near_capacity':
                return $slot->isOccupied() && $slot->isNearCapacity();
            case 'at_capacity':
                return $slot->isOccupied() && $slot->isAtCapacity();
            default:
                return true;
        }
    }

    /**
     * Get available menu items for the branch
     */
    protected function getAvailableMenuItems()
    {
        return MenuItem::where('branch_id', $this->branch->id)
            ->where('is_available', 1)
            ->orderBy('item_name')
            ->get();
    }

    /**
     * Open allocation modal for a slot
     */
    public function openAllocationModal($slotId)
    {
        $this->selectedSlotId = $slotId;
        $this->selectedMenuItemId = null;
        $this->newQuantity = 0;
        $this->errorMessage = '';
        $this->successMessage = '';
        $this->showAllocationModal = true;
    }

    /**
     * Close allocation modal
     */
    public function closeAllocationModal()
    {
        $this->showAllocationModal = false;
        $this->selectedSlotId = null;
        $this->selectedMenuItemId = null;
        $this->newQuantity = 0;
        $this->errorMessage = '';
    }

    /**
     * Open quantity adjustment modal for a slot
     */
    public function openQuantityModal($slotId, $action = 'set')
    {
        $slot = Slot::find($slotId);
        if (!$slot || !$slot->isOccupied()) {
            $this->errorMessage = 'Slot not found or not occupied.';
            return;
        }

        $this->currentSlot = $slot;
        $this->quantityAction = $action;
        $this->quantityToAdd = 1;
        $this->quantityToRemove = 1;
        $this->newQuantity = $slot->getCurrentQuantity();
        $this->errorMessage = '';
        $this->successMessage = '';
        $this->showQuantityModal = true;
    }

    /**
     * Close quantity adjustment modal
     */
    public function closeQuantityModal()
    {
        $this->showQuantityModal = false;
        $this->currentSlot = null;
        $this->quantityAction = 'set';
        $this->quantityToAdd = 1;
        $this->quantityToRemove = 1;
        $this->newQuantity = 0;
        $this->errorMessage = '';
    }

    /**
     * Allocate menu item to slot
     */
    public function allocateMenuItem()
    {
        try {
            // Check permissions
            $permissionService = app(InventoryPermissionService::class);
            if (!$permissionService->canAllocateInventory(auth()->user())) {
                $this->errorMessage = 'Insufficient permissions to allocate inventory.';
                return;
            }

            $this->errorMessage = '';
            $this->successMessage = '';

            if (!$this->selectedSlotId || !$this->selectedMenuItemId) {
                $this->errorMessage = 'Please select both a slot and menu item.';
                return;
            }

            $slot = Slot::find($this->selectedSlotId);
            $menuItem = MenuItem::find($this->selectedMenuItemId);

            if (!$slot || !$menuItem) {
                $this->errorMessage = 'Slot or menu item not found.';
                return;
            }

            // Validate allocation
            $errors = $this->validationService->validateSlotAllocation($slot, $menuItem, $this->newQuantity);
            if (!empty($errors)) {
                $this->errorMessage = implode(' ', $errors);
                return;
            }

            // Perform allocation
            $this->inventoryService->allocateMenuItemToSlot($slot, $menuItem, $this->newQuantity);

            $this->successMessage = "Successfully allocated {$menuItem->name} to {$slot->name}.";
            $this->closeAllocationModal();
            
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
        }
    }

    /**
     * Deallocate menu item from slot
     */
    public function deallocateMenuItem($slotId)
    {
        try {
            // Check permissions
            $permissionService = app(InventoryPermissionService::class);
            if (!$permissionService->canAllocateInventory(auth()->user())) {
                $this->errorMessage = 'Insufficient permissions to deallocate inventory.';
                return;
            }

            $this->errorMessage = '';
            $this->successMessage = '';

            $slot = Slot::find($slotId);
            if (!$slot) {
                $this->errorMessage = 'Slot not found.';
                return;
            }

            // Validate deallocation
            $errors = $this->validationService->validateSlotDeallocation($slot);
            if (!empty($errors)) {
                $this->errorMessage = implode(' ', $errors);
                return;
            }

            $menuItemName = $slot->getAssignedMenuItem()->name ?? 'Unknown';
            
            // Perform deallocation
            $this->inventoryService->deallocateMenuItemFromSlot($slot);

            $this->successMessage = "Successfully deallocated {$menuItemName} from {$slot->name}.";
            
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
        }
    }

    /**
     * Add inventory to slot
     */
    public function addInventory()
    {
        try {
            $this->errorMessage = '';
            $this->successMessage = '';

            if (!$this->currentSlot || $this->quantityToAdd <= 0) {
                $this->errorMessage = 'Invalid quantity to add.';
                return;
            }

            // Validate addition
            $errors = $this->validationService->validateInventoryAddition($this->currentSlot, $this->quantityToAdd);
            if (!empty($errors)) {
                $this->errorMessage = implode(' ', $errors);
                return;
            }

            // Perform addition
            $this->inventoryService->addInventoryToSlot($this->currentSlot, $this->quantityToAdd);

            $this->successMessage = "Successfully added {$this->quantityToAdd} units.";
            $this->currentSlot = $this->currentSlot->fresh();
            $this->newQuantity = $this->currentSlot->getCurrentQuantity();
            $this->quantityToAdd = 1;
            
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
        }
    }

    /**
     * Remove inventory from slot
     */
    public function removeInventory()
    {
        try {
            $this->errorMessage = '';
            $this->successMessage = '';

            if (!$this->currentSlot || $this->quantityToRemove <= 0) {
                $this->errorMessage = 'Invalid quantity to remove.';
                return;
            }

            // Validate removal
            $errors = $this->validationService->validateInventoryRemoval($this->currentSlot, $this->quantityToRemove);
            if (!empty($errors)) {
                $this->errorMessage = implode(' ', $errors);
                return;
            }

            // Perform removal
            $this->inventoryService->removeInventoryFromSlot($this->currentSlot, $this->quantityToRemove);

            $this->successMessage = "Successfully removed {$this->quantityToRemove} units.";
            $this->currentSlot = $this->currentSlot->fresh();
            $this->newQuantity = $this->currentSlot->getCurrentQuantity();
            $this->quantityToRemove = 1;
            
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
        }
    }

    /**
     * Set inventory quantity for slot
     */
    public function setInventoryQuantity()
    {
        try {
            $this->errorMessage = '';
            $this->successMessage = '';

            if (!$this->currentSlot || $this->newQuantity < 0) {
                $this->errorMessage = 'Invalid quantity.';
                return;
            }

            // Validate quantity setting
            $errors = $this->validationService->validateInventoryQuantitySetting($this->currentSlot, $this->newQuantity);
            if (!empty($errors)) {
                $this->errorMessage = implode(' ', $errors);
                return;
            }

            $oldQuantity = $this->currentSlot->getCurrentQuantity();
            
            // Perform quantity setting
            $this->inventoryService->setInventoryQuantity($this->currentSlot, $this->newQuantity);

            $this->successMessage = "Successfully updated quantity from {$oldQuantity} to {$this->newQuantity}.";
            $this->currentSlot = $this->currentSlot->fresh();
            
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
        }
    }

    /**
     * Update quantity based on action
     */
    public function updateQuantity()
    {
        switch ($this->quantityAction) {
            case 'add':
                $this->addInventory();
                break;
            case 'remove':
                $this->removeInventory();
                break;
            case 'set':
                $this->setInventoryQuantity();
                break;
        }
    }

    /**
     * Quick add inventory (add 1 unit)
     */
    public function quickAddInventory($slotId)
    {
        try {
            $slot = Slot::find($slotId);
            if (!$slot || !$slot->isOccupied()) {
                $this->errorMessage = 'Slot not found or not occupied.';
                return;
            }

            // Validate addition
            $errors = $this->validationService->validateInventoryAddition($slot, 1);
            if (!empty($errors)) {
                $this->errorMessage = implode(' ', $errors);
                return;
            }

            // Perform addition
            $this->inventoryService->addInventoryToSlot($slot, 1);
            $this->successMessage = "Successfully added 1 unit to {$slot->name}.";
            
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
        }
    }

    /**
     * Quick remove inventory (remove 1 unit)
     */
    public function quickRemoveInventory($slotId)
    {
        try {
            $slot = Slot::find($slotId);
            if (!$slot || !$slot->isOccupied()) {
                $this->errorMessage = 'Slot not found or not occupied.';
                return;
            }

            // Validate removal
            $errors = $this->validationService->validateInventoryRemoval($slot, 1);
            if (!empty($errors)) {
                $this->errorMessage = implode(' ', $errors);
                return;
            }

            // Perform removal
            $this->inventoryService->removeInventoryFromSlot($slot, 1);
            $this->successMessage = "Successfully removed 1 unit from {$slot->name}.";
            
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
        }
    }

    /**
     * Clear all filters including menu item filter
     */
    public function clearFilters()
    {
        $this->searchTerm = '';
        $this->filterStatus = 'all';
        $this->menuItemFilter = 'all';
        $this->selectedTrayId = null;
        $this->resetPage();
    }

    /**
     * Clear messages
     */
    public function clearMessages()
    {
        $this->errorMessage = '';
        $this->successMessage = '';
    }

    /**
     * Update search term
     */
    public function updatedSearchTerm()
    {
        $this->resetPage();
    }

    /**
     * Update filter status
     */
    public function updatedFilterStatus()
    {
        $this->resetPage();
    }

    /**
     * Update selected tray
     */
    public function updatedSelectedTrayId()
    {
        $this->resetPage();
    }

    /**
     * Get slot status badge class
     */
    public function getSlotStatusClass(Slot $slot): string
    {
        if (!$slot->isOccupied()) {
            return 'bg-gray-100 text-gray-800';
        }

        if ($slot->isAtCapacity()) {
            return 'bg-red-100 text-red-800';
        }

        if ($slot->isNearCapacity()) {
            return 'bg-yellow-100 text-yellow-800';
        }

        return 'bg-green-100 text-green-800';
    }

    /**
     * Get slot status text
     */
    public function getSlotStatusText(Slot $slot): string
    {
        if (!$slot->isOccupied()) {
            return 'Available';
        }

        if ($slot->isAtCapacity()) {
            return 'At Capacity';
        }

        if ($slot->isNearCapacity()) {
            return 'Near Capacity';
        }

        return 'In Use';
    }

    /**
     * Get summary statistics for dashboard widgets
     */
    private function getSummaryStats(): array
    {
        $summary = $this->inventoryService->getBranchInventorySummary($this->branch);
        
        return [
            'total_capacity' => $summary['total_capacity'] ?? 0,
            'total_inventory' => $summary['current_inventory'] ?? $summary['total_inventory'] ?? 0,
            'overall_utilization' => $summary['current_utilization'] ?? $summary['overall_utilization'] ?? 0,
            'occupied_slots' => $summary['occupied_slots'] ?? 0,
            'total_slots' => $summary['total_slots'] ?? 0,
            'total_trays' => $summary['total_trays'] ?? 0,
        ];
    }

    /**
     * Get menu items for filter dropdown
     */
    private function getMenuItemsForFilter()
    {
        return MenuItem::where('branch_id', $this->branch->id)
            ->whereHas('inventoryAllocations.slot.tray', function ($query) {
                $query->where('branch_id', $this->branch->id);
            })
            ->orderBy('item_name')
            ->get();
    }

    // ===== TRAY MANAGEMENT METHODS =====

    /**
     * Show add tray modal
     */
    public function showAddTray()
    {
        $this->resetTrayForm();
        $this->showAddTrayModal = true;
    }

    /**
     * Show edit tray modal
     */
    public function showEditTray($trayId)
    {
        $this->selectedTray = Tray::findOrFail($trayId);
        $this->trayName = $this->selectedTray->name;
        $this->traySortOrder = $this->selectedTray->sort_order;
        $this->showEditTrayModal = true;
    }

    /**
     * Save tray (create or update)
     */
    public function saveTray()
    {
        try {
            // Validate at Livewire level first
            $this->validate();
            
            $data = [
                'name' => $this->trayName,
                'sort_order' => $this->traySortOrder ?: null,
                'branch_id' => $this->branch->id
            ];

            if ($this->selectedTray) {
                $this->inventoryService->updateTray($this->selectedTray, $data);
                $this->alert('success', 'Tray updated successfully!');
            } else {
                $this->inventoryService->createTray($this->branch, $data);
                $this->alert('success', 'Tray created successfully!');
            }

            $this->resetTrayForm();
            $this->showAddTrayModal = false;
            $this->showEditTrayModal = false;
            
        } catch (ValidationException $e) {
            $this->alert('error', 'Validation Error: ' . implode(', ', $e->validator->errors()->all()));
        } catch (\Exception $e) {
            $this->alert('error', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * Confirm delete tray
     */
    public function confirmDeleteTray($trayId)
    {
        $tray = Tray::findOrFail($trayId);
        $this->deleteType = 'tray';
        $this->deleteItem = $tray;
        $this->deleteWarnings = $this->inventoryService->validateConfigurationChange('delete_tray', $tray, []);
        $this->showDeleteConfirmation = true;
    }

    /**
     * Delete tray
     */
    public function deleteTray($forceDelete = false)
    {
        try {
            $this->inventoryService->deleteTray($this->deleteItem, $forceDelete);
            $this->alert('success', 'Tray deleted successfully!');
            $this->showDeleteConfirmation = false;
            $this->resetDeleteForm();
        } catch (\Exception $e) {
            $this->alert('error', 'Error: ' . $e->getMessage());
        }
    }

    // ===== SLOT MANAGEMENT METHODS =====

    /**
     * Show add slot modal
     */
    public function showAddSlot($trayId)
    {
        $this->resetSlotForm();
        $this->selectedTrayForSlot = Tray::findOrFail($trayId);
        $this->showAddSlotModal = true;
    }

    /**
     * Show edit slot modal
     */
    public function showEditSlot($slotId)
    {
        $this->selectedSlot = Slot::with('tray')->findOrFail($slotId);
        $this->selectedTrayForSlot = $this->selectedSlot->tray;
        $this->slotName = $this->selectedSlot->name;
        $this->slotCapacity = $this->selectedSlot->capacity;
        $this->slotSortOrder = $this->selectedSlot->sort_order;
        $this->showEditSlotModal = true;
    }

    /**
     * Save slot (create or update)
     */
    public function saveSlot()
    {
        try {
            // Validate at Livewire level first
            $this->validate();
            
            $data = [
                'name' => $this->slotName,
                'capacity' => (int) $this->slotCapacity,
                'sort_order' => $this->slotSortOrder ?: null,
                'tray_id' => $this->selectedTrayForSlot->id
            ];

            if ($this->selectedSlot) {
                $this->inventoryService->updateSlot($this->selectedSlot, $data);
                $this->alert('success', 'Slot updated successfully!');
            } else {
                $this->inventoryService->createSlot($this->selectedTrayForSlot, $data);
                $this->alert('success', 'Slot created successfully!');
            }

            $this->resetSlotForm();
            $this->showAddSlotModal = false;
            $this->showEditSlotModal = false;
            
        } catch (ValidationException $e) {
            $this->alert('error', 'Validation Error: ' . implode(', ', $e->validator->errors()->all()));
        } catch (\Exception $e) {
            $this->alert('error', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * Confirm delete slot
     */
    public function confirmDeleteSlot($slotId)
    {
        $slot = Slot::findOrFail($slotId);
        $this->deleteType = 'slot';
        $this->deleteItem = $slot;
        $this->deleteWarnings = $this->inventoryService->validateConfigurationChange('delete_slot', $slot, []);
        $this->showDeleteConfirmation = true;
    }

    /**
     * Delete slot
     */
    public function deleteSlot($forceDelete = false)
    {
        try {
            $this->inventoryService->deleteSlot($this->deleteItem, $forceDelete);
            $this->alert('success', 'Slot deleted successfully!');
            $this->showDeleteConfirmation = false;
            $this->resetDeleteForm();
        } catch (\Exception $e) {
            $this->alert('error', 'Error: ' . $e->getMessage());
        }
    }

    // ===== HELPER METHODS =====

    /**
     * Reset tray form
     */
    private function resetTrayForm()
    {
        $this->trayName = '';
        $this->traySortOrder = '';
        $this->selectedTray = null;
    }

    /**
     * Reset slot form
     */
    private function resetSlotForm()
    {
        $this->slotName = '';
        $this->slotCapacity = '';
        $this->slotSortOrder = '';
        $this->selectedSlot = null;
        $this->selectedTrayForSlot = null;
    }

    /**
     * Reset delete form
     */
    private function resetDeleteForm()
    {
        $this->deleteType = '';
        $this->deleteItem = null;
        $this->deleteWarnings = [];
    }

    /**
     * Cancel modal
     */
    #[On('cancelModal')]
    public function cancelModal()
    {
        $this->showAddTrayModal = false;
        $this->showEditTrayModal = false;
        $this->showAddSlotModal = false;
        $this->showEditSlotModal = false;
        $this->showDeleteConfirmation = false;
        $this->resetTrayForm();
        $this->resetSlotForm();
        $this->resetDeleteForm();
    }


    /**
     * Update menu item filter
     */
    public function updatedMenuItemFilter()
    {
        $this->resetPage();
    }

    // ===== VALIDATION RULES =====

    /**
     * Get validation rules
     */
    protected function rules()
    {
        $rules = [];
        
        if ($this->showAddTrayModal || $this->showEditTrayModal) {
            $rules = array_merge($rules, [
                'trayName' => 'required|string|max:255',
                'traySortOrder' => 'nullable|integer|min:0'
            ]);
        }
        
        if ($this->showAddSlotModal || $this->showEditSlotModal) {
            $rules = array_merge($rules, [
                'slotName' => 'required|string|max:255',
                'slotCapacity' => 'required|integer|min:1',
                'slotSortOrder' => 'nullable|integer|min:0'
            ]);
        }
        
        return $rules;
    }

    /**
     * Get validation messages
     */
    protected function messages()
    {
        return [
            'trayName.required' => 'Tray name is required.',
            'trayName.max' => 'Tray name cannot exceed 255 characters.',
            'traySortOrder.integer' => 'Sort order must be a number.',
            'traySortOrder.min' => 'Sort order cannot be negative.',
            'slotName.required' => 'Slot name is required.',
            'slotName.max' => 'Slot name cannot exceed 255 characters.',
            'slotCapacity.required' => 'Slot capacity is required.',
            'slotCapacity.integer' => 'Capacity must be a number.',
            'slotCapacity.min' => 'Capacity must be at least 1.',
            'slotSortOrder.integer' => 'Sort order must be a number.',
            'slotSortOrder.min' => 'Sort order cannot be negative.'
        ];
    }
}
