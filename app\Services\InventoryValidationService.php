<?php

namespace App\Services;

use App\Models\Slot;
use App\Models\MenuItem;
use App\Models\InventoryAllocation;
use App\Models\Branch;

class InventoryValidationService
{
    /**
     * Validate capacity constraints for a slot
     */
    public function validateCapacityConstraints(Slot $slot, int $quantity): array
    {
        $errors = [];

        if ($quantity < 0) {
            $errors[] = 'Quantity cannot be negative.';
        }

        if ($quantity > $slot->capacity) {
            $errors[] = "Quantity ({$quantity}) exceeds slot capacity ({$slot->capacity}).";
        }

        return $errors;
    }

    /**
     * Validate that a slot can only contain one menu item type
     */
    public function validateSingleMenuItemPerSlot(Slot $slot, MenuItem $menuItem): array
    {
        $errors = [];
        
        $existingAllocation = $slot->inventoryAllocation;
        
        if ($existingAllocation && $existingAllocation->menu_item_id !== $menuItem->id) {
            $existingMenuItem = $existingAllocation->menuItem;
            $errors[] = "Slot is already occupied by '{$existingMenuItem->name}'. Each slot can only contain one menu item type.";
        }

        return $errors;
    }

    /**
     * Validate that a menu item occupies at least one slot
     */
    public function validateMinimumSlotRequirement(MenuItem $menuItem): array
    {
        $errors = [];
        
        if (!$menuItem->hasInventoryAllocated()) {
            $errors[] = "Menu item '{$menuItem->name}' must be allocated to at least one slot.";
        }

        return $errors;
    }

    /**
     * Validate inventory addition operation
     */
    public function validateInventoryAddition(Slot $slot, int $quantity): array
    {
        $errors = [];

        if ($quantity <= 0) {
            $errors[] = 'Addition quantity must be positive.';
            return $errors;
        }

        $allocation = $slot->inventoryAllocation;
        if (!$allocation) {
            $errors[] = 'Cannot add inventory to unoccupied slot.';
            return $errors;
        }

        $newTotal = $allocation->current_quantity + $quantity;
        if ($newTotal > $slot->capacity) {
            $available = $slot->capacity - $allocation->current_quantity;
            $errors[] = "Cannot add {$quantity} units. Only {$available} units available in slot.";
        }

        return $errors;
    }

    /**
     * Validate inventory removal operation
     */
    public function validateInventoryRemoval(Slot $slot, int $quantity): array
    {
        $errors = [];

        if ($quantity <= 0) {
            $errors[] = 'Removal quantity must be positive.';
            return $errors;
        }

        $allocation = $slot->inventoryAllocation;
        if (!$allocation) {
            $errors[] = 'Cannot remove inventory from unoccupied slot.';
            return $errors;
        }

        if ($quantity > $allocation->current_quantity) {
            $errors[] = "Cannot remove {$quantity} units. Only {$allocation->current_quantity} units available.";
        }

        return $errors;
    }

    /**
     * Validate inventory quantity setting operation
     */
    public function validateInventoryQuantitySetting(Slot $slot, int $quantity): array
    {
        $errors = [];

        if ($quantity < 0) {
            $errors[] = 'Quantity cannot be negative.';
        }

        $allocation = $slot->inventoryAllocation;
        if (!$allocation) {
            $errors[] = 'Cannot set inventory for unoccupied slot.';
            return $errors;
        }

        if ($quantity > $slot->capacity) {
            $errors[] = "Quantity ({$quantity}) exceeds slot capacity ({$slot->capacity}).";
        }

        return $errors;
    }

    /**
     * Validate menu item allocation to slot (alias for validateSlotAllocation)
     */
    public function validateMenuItemAllocation(Slot $slot, MenuItem $menuItem, int $initialQuantity = 0): array
    {
        return $this->validateSlotAllocation($slot, $menuItem, $initialQuantity);
    }

    /**
     * Validate slot allocation operation
     */
    public function validateSlotAllocation(Slot $slot, MenuItem $menuItem, int $initialQuantity = 0): array
    {
        $errors = [];

        // Check if slot is already occupied
        $slotErrors = $this->validateSingleMenuItemPerSlot($slot, $menuItem);
        $errors = array_merge($errors, $slotErrors);

        // Check capacity constraints
        $capacityErrors = $this->validateCapacityConstraints($slot, $initialQuantity);
        $errors = array_merge($errors, $capacityErrors);

        // Check if menu item and slot belong to the same branch
        if ($slot->tray->branch_id !== $menuItem->branch_id) {
            $errors[] = 'Menu item and slot must belong to the same branch.';
        }

        return $errors;
    }

    /**
     * Validate slot deallocation operation
     */
    public function validateSlotDeallocation(Slot $slot): array
    {
        $errors = [];

        if (!$slot->isOccupied()) {
            $errors[] = 'Cannot deallocate unoccupied slot.';
        }

        return $errors;
    }

    /**
     * Validate menu item inventory requirements
     */
    public function validateMenuItemInventoryRequirements(MenuItem $menuItem): array
    {
        $errors = [];

        // Check minimum slot requirement
        $minSlotErrors = $this->validateMinimumSlotRequirement($menuItem);
        $errors = array_merge($errors, $minSlotErrors);

        // Check if all allocations are valid
        foreach ($menuItem->inventoryAllocations as $allocation) {
            $slot = $allocation->slot;
            
            // Validate capacity constraints
            $capacityErrors = $this->validateCapacityConstraints($slot, $allocation->current_quantity);
            $errors = array_merge($errors, $capacityErrors);
            
            // Validate single menu item per slot
            $slotErrors = $this->validateSingleMenuItemPerSlot($slot, $menuItem);
            $errors = array_merge($errors, $slotErrors);
        }

        return $errors;
    }

    /**
     * Validate branch inventory configuration
     */
    public function validateBranchInventoryConfiguration(Branch $branch): array
    {
        $errors = [];

        foreach ($branch->trays as $tray) {
            foreach ($tray->slots as $slot) {
                if ($slot->isOccupied()) {
                    $allocation = $slot->inventoryAllocation;
                    $menuItem = $allocation->menuItem;
                    
                    // Validate capacity constraints
                    $capacityErrors = $this->validateCapacityConstraints($slot, $allocation->current_quantity);
                    $errors = array_merge($errors, $capacityErrors);
                    
                    // Validate single menu item per slot
                    $slotErrors = $this->validateSingleMenuItemPerSlot($slot, $menuItem);
                    $errors = array_merge($errors, $slotErrors);
                    
                    // Validate branch consistency
                    if ($menuItem->branch_id !== $branch->id) {
                        $errors[] = "Menu item '{$menuItem->name}' in slot '{$slot->name}' does not belong to this branch.";
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * Validate inventory operation constraints
     */
    public function validateInventoryOperation(string $operation, Slot $slot, array $params = []): array
    {
        $errors = [];

        switch ($operation) {
            case 'add':
                $quantity = $params['quantity'] ?? 0;
                $errors = $this->validateInventoryAddition($slot, $quantity);
                break;

            case 'remove':
                $quantity = $params['quantity'] ?? 0;
                $errors = $this->validateInventoryRemoval($slot, $quantity);
                break;

            case 'set':
                $quantity = $params['quantity'] ?? 0;
                $errors = $this->validateInventoryQuantitySetting($slot, $quantity);
                break;

            case 'allocate':
                $menuItem = $params['menu_item'] ?? null;
                $quantity = $params['quantity'] ?? 0;
                if ($menuItem) {
                    $errors = $this->validateSlotAllocation($slot, $menuItem, $quantity);
                } else {
                    $errors[] = 'Menu item is required for allocation operation.';
                }
                break;

            case 'deallocate':
                $errors = $this->validateSlotDeallocation($slot);
                break;

            default:
                $errors[] = "Unknown inventory operation: {$operation}";
        }

        return $errors;
    }

    /**
     * Check if inventory operation is safe (no data loss)
     */
    public function isInventoryOperationSafe(string $operation, Slot $slot, array $params = []): bool
    {
        switch ($operation) {
            case 'deallocate':
                // Safe if slot has no inventory or user confirms data loss
                return !$slot->isOccupied() || $slot->getCurrentQuantity() === 0;

            case 'reduce_capacity':
                $newCapacity = $params['capacity'] ?? 0;
                return $newCapacity >= $slot->getCurrentQuantity();

            case 'delete_slot':
                // Safe if slot is not occupied
                return !$slot->isOccupied();

            default:
                return true; // Most operations are safe
        }
    }

    /**
     * Get validation summary for multiple operations
     */
    public function getValidationSummary(array $operations): array
    {
        $summary = [
            'total_operations' => count($operations),
            'valid_operations' => 0,
            'invalid_operations' => 0,
            'errors' => [],
            'warnings' => []
        ];

        foreach ($operations as $operation) {
            $errors = $this->validateInventoryOperation(
                $operation['type'],
                $operation['slot'],
                $operation['params'] ?? []
            );

            if (empty($errors)) {
                $summary['valid_operations']++;
            } else {
                $summary['invalid_operations']++;
                $summary['errors'] = array_merge($summary['errors'], $errors);
            }

            // Check for warnings (unsafe operations)
            if (!$this->isInventoryOperationSafe($operation['type'], $operation['slot'], $operation['params'] ?? [])) {
                $summary['warnings'][] = "Operation '{$operation['type']}' may cause data loss.";
            }
        }

        return $summary;
    }
}