# Inventory Permission System

## Overview

The inventory permission system provides comprehensive access control for branch-specific inventory management. It ensures that users can only access and modify inventory data for their assigned branch while enforcing role-based permissions.

## Components

### 1. Middleware

#### InventoryAccess Middleware
- **Purpose**: Protects inventory routes with permission checks
- **Usage**: `middleware('inventory.access:permission_name')`
- **Features**:
  - Validates user authentication
  - Checks branch assignment
  - Enforces specific permissions
  - Adds branch_id to request for automatic filtering

#### BranchDataIsolation Middleware
- **Purpose**: Ensures branch-specific data isolation
- **Usage**: `middleware('branch.isolation')`
- **Features**:
  - Validates user has assigned branch
  - Adds user_branch_id to request
  - Prevents cross-branch data access

### 2. Services

#### InventoryPermissionService
- **Purpose**: Centralized permission checking logic
- **Key Methods**:
  - `canViewDashboard(User $user): bool`
  - `canConfigureInventory(User $user): bool`
  - `canManageAllocation(User $user): bool`
  - `canUpdateTray(User $user, Tray $tray): bool`
  - `canDeleteTray(User $user, Tray $tray): bool`
  - `canAccessAllocation(User $user, InventoryAllocation $allocation): bool`

### 3. Traits

#### BranchScoped Trait
- **Purpose**: Automatic branch-based query filtering
- **Usage**: Add `use BranchScoped;` to model
- **Features**:
  - Global scope for branch filtering
  - Works with direct branch_id or relationships
  - `belongsToUserBranch()` method for ownership checks

### 4. Helpers

#### InventoryPermissionHelper
- **Purpose**: Static helper methods for views and controllers
- **Key Methods**:
  - `canViewDashboard(): bool`
  - `canConfigureInventory(): bool`
  - `canManageAllocation(): bool`
  - `getUserPermissions(): array`

### 5. Blade Directives

Custom Blade directives for view-level permission checks:

```blade
@canViewInventoryDashboard
    <!-- Dashboard content -->
@endcanViewInventoryDashboard

@canConfigureInventory
    <!-- Configuration buttons -->
@endcanConfigureInventory

@canManageInventoryAllocation
    <!-- Allocation controls -->
@endcanManageInventoryAllocation
```

### 6. Global Helper Function

```php
// Check permissions anywhere in the application
if (inventory_can('view_dashboard')) {
    // User can view dashboard
}
```

## Permissions

### Available Permissions

1. **View Inventory Dashboard** - View inventory overview
2. **Configure Inventory** - Manage trays and slots configuration
3. **Manage Inventory Allocation** - Access allocation management interface
4. **Create Tray** - Create new trays
5. **Update Tray** - Modify existing trays
6. **Delete Tray** - Remove trays (only if no occupied slots)
7. **Create Slot** - Create new slots
8. **Update Slot** - Modify existing slots
9. **Delete Slot** - Remove slots (only if not occupied)
10. **Allocate Inventory** - Assign menu items to slots
11. **Adjust Inventory** - Modify inventory quantities

### Default Roles

#### Branch Manager
- **Permissions**: All inventory permissions
- **Scope**: Full access to branch inventory

#### Inventory Manager
- **Permissions**: Configuration and allocation permissions
- **Scope**: Can configure and manage inventory but not delete occupied resources

#### Staff
- **Permissions**: View-only access
- **Scope**: Can view inventory dashboard only

## Usage Examples

### Route Protection

```php
Route::prefix('inventory')->middleware(['branch.isolation'])->group(function () {
    Route::get('/', InventoryController::class)
        ->middleware('inventory.access:View Inventory Dashboard');
    
    Route::get('/configuration', ConfigurationController::class)
        ->middleware('inventory.access:Configure Inventory');
});
```

### Controller Usage

```php
public function index()
{
    $permissionService = app(InventoryPermissionService::class);
    
    if (!$permissionService->canViewDashboard(auth()->user())) {
        abort(403);
    }
    
    // Controller logic
}
```

### Livewire Component

```php
public function mount()
{
    $permissionService = app(InventoryPermissionService::class);
    
    if (!$permissionService->canManageAllocation(auth()->user())) {
        abort(403, 'Insufficient permissions');
    }
}

public function updateTray($trayId)
{
    $tray = Tray::findOrFail($trayId);
    $permissionService = app(InventoryPermissionService::class);
    
    if (!$permissionService->canUpdateTray(auth()->user(), $tray)) {
        $this->errorMessage = 'Cannot update this tray';
        return;
    }
    
    // Update logic
}
```

### View Usage

```blade
@canViewInventoryDashboard
    <a href="{{ route('inventory.index') }}">Dashboard</a>
@endcanViewInventoryDashboard

@canConfigureInventory
    <button wire:click="openConfigModal">Configure</button>
@endcanConfigureInventory

@if(inventory_can('create_tray'))
    <button wire:click="createTray">Add Tray</button>
@endif
```

## Branch Data Isolation

The system automatically ensures that:

1. **Users can only access their assigned branch data**
2. **Global scopes filter queries by branch**
3. **Permission checks include branch ownership validation**
4. **Cross-branch access is prevented at multiple levels**

### Model Scoping

```php
// Automatically filtered by user's branch
$trays = Tray::all(); // Only returns trays from user's branch

// Explicit branch filtering
$trays = Tray::forBranch($branchId)->get();

// Check ownership
if ($tray->belongsToUserBranch()) {
    // Safe to modify
}
```

## Testing

Comprehensive test coverage includes:

- Middleware functionality
- Permission service methods
- Branch data isolation
- Route access control
- Livewire component permissions
- Helper functions
- Complete integration scenarios

## Security Considerations

1. **Multiple layers of protection** (middleware, service, model scopes)
2. **Branch isolation at database level**
3. **Permission checks before any data modification**
4. **Automatic request filtering**
5. **Comprehensive audit trail through Laravel's built-in logging**

## Installation

1. Run migrations for permissions and roles
2. Seed modules and permissions: `php artisan db:seed --class=ModuleSeeder,PermissionSeeder`
3. Create inventory roles: `php artisan db:seed --class=InventoryRoleSeeder`
4. Assign roles to users as needed
5. Apply middleware to routes
6. Use permission checks in controllers and views