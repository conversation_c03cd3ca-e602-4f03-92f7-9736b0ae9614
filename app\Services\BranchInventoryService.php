<?php

namespace App\Services;

use App\Models\Branch;
use App\Models\Tray;
use App\Models\Slot;
use App\Models\InventoryAllocation;
use App\Models\MenuItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class BranchInventoryService
{
    /**
     * Create a new tray for a branch
     */
    public function createTray(Branch $branch, array $data): Tray
    {
        $validator = Validator::make($data, Tray::validationRules(), Tray::validationMessages());
        
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        $data['branch_id'] = $branch->id;
        
        // Set sort_order if not provided
        if (!isset($data['sort_order'])) {
            $data['sort_order'] = $branch->trays()->max('sort_order') + 1;
        }

        return Tray::create($data);
    }

    /**
     * Update an existing tray
     */
    public function updateTray(Tray $tray, array $data): Tray
    {
        $validator = Validator::make($data, Tray::validationRules($tray->id), Tray::validationMessages());
        
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        $tray->update($data);
        return $tray->fresh();
    }

    /**
     * Delete a tray (with validation for data loss)
     */
    public function deleteTray(Tray $tray, bool $forceDelete = false): bool
    {
        if (!$forceDelete && $tray->hasOccupiedSlots()) {
            throw new \Exception('Cannot delete tray with occupied slots. This would cause data loss.');
        }

        return DB::transaction(function () use ($tray) {
            // Delete all slots and their allocations
            $tray->slots()->each(function ($slot) {
                $allocation = $slot->inventoryAllocation;
                if ($allocation) {
                    $allocation->delete();
                }
                $slot->delete();
            });
            
            return $tray->delete();
        });
    }

    /**
     * Create a new slot for a tray
     */
    public function createSlot(Tray $tray, array $data): Slot
    {
        $validator = Validator::make($data, Slot::validationRules(), Slot::validationMessages());
        
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        $data['tray_id'] = $tray->id;
        
        // Set sort_order if not provided
        if (!isset($data['sort_order'])) {
            $data['sort_order'] = $tray->slots()->max('sort_order') + 1;
        }

        return Slot::create($data);
    }

    /**
     * Update an existing slot
     */
    public function updateSlot(Slot $slot, array $data): Slot
    {
        $validator = Validator::make($data, Slot::validationRules($slot->id), Slot::validationMessages());
        
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // Check if reducing capacity would cause data loss
        if (isset($data['capacity']) && $data['capacity'] < $slot->getCurrentQuantity()) {
            throw new \Exception('Cannot reduce slot capacity below current inventory quantity. This would cause data loss.');
        }

        $slot->update($data);
        return $slot->fresh();
    }

    /**
     * Delete a slot (with validation for data loss)
     */
    public function deleteSlot(Slot $slot, bool $forceDelete = false): bool
    {
        if (!$forceDelete && $slot->isOccupied()) {
            throw new \Exception('Cannot delete occupied slot. This would cause data loss.');
        }

        return DB::transaction(function () use ($slot) {
            // Delete inventory allocation if exists
            $allocation = $slot->inventoryAllocation;
            if ($allocation) {
                $allocation->delete();
            }
            
            return $slot->delete();
        });
    }

    /**
     * Allocate a menu item to a slot
     */
    public function allocateMenuItemToSlot(Slot $slot, MenuItem $menuItem, int $initialQuantity = 0): InventoryAllocation
    {
        if ($slot->isOccupied()) {
            throw new \Exception('Slot is already occupied by another menu item.');
        }

        if ($initialQuantity > $slot->capacity) {
            throw new \Exception('Initial quantity exceeds slot capacity.');
        }

        if ($initialQuantity < 0) {
            throw new \Exception('Initial quantity cannot be negative.');
        }

        $data = [
            'slot_id' => $slot->id,
            'menu_item_id' => $menuItem->id,
            'current_quantity' => $initialQuantity
        ];

        $validator = Validator::make($data, InventoryAllocation::validationRules(), InventoryAllocation::validationMessages());
        
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return InventoryAllocation::create($data);
    }

    /**
     * Deallocate a menu item from a slot
     */
    public function deallocateMenuItemFromSlot(Slot $slot): bool
    {
        $allocation = $slot->inventoryAllocation;
        
        if (!$allocation) {
            throw new \Exception('Slot is not occupied.');
        }

        return $allocation->delete();
    }

    /**
     * Add inventory to a slot
     */
    public function addInventoryToSlot(Slot $slot, int $quantity): InventoryAllocation
    {
        $allocation = $slot->inventoryAllocation;
        
        if (!$allocation) {
            throw new \Exception('Slot is not occupied. Cannot add inventory.');
        }

        if (!$allocation->canAddQuantity($quantity)) {
            throw new \Exception('Cannot add quantity. Would exceed slot capacity.');
        }

        $allocation->addInventory($quantity);
        return $allocation->fresh();
    }

    /**
     * Remove inventory from a slot
     */
    public function removeInventoryFromSlot(Slot $slot, int $quantity): InventoryAllocation
    {
        $allocation = $slot->inventoryAllocation;
        
        if (!$allocation) {
            throw new \Exception('Slot is not occupied. Cannot remove inventory.');
        }

        if (!$allocation->canRemoveQuantity($quantity)) {
            throw new \Exception('Cannot remove quantity. Would result in negative inventory.');
        }

        $allocation->removeInventory($quantity);
        return $allocation->fresh();
    }

    /**
     * Set inventory quantity for a slot
     */
    public function setInventoryQuantity(Slot $slot, int $quantity): InventoryAllocation
    {
        $allocation = $slot->inventoryAllocation;
        
        if (!$allocation) {
            throw new \Exception('Slot is not occupied. Cannot set inventory.');
        }

        if (!$allocation->setQuantity($quantity)) {
            throw new \Exception('Cannot set quantity. Invalid quantity or exceeds capacity.');
        }

        return $allocation->fresh();
    }

    /**
     * Get branch inventory summary
     */
    public function getBranchInventorySummary(Branch $branch): array
    {
        $totalCapacity = $branch->getTotalInventoryCapacity();
        
        // Get current inventory through trays -> slots -> allocations
        $currentInventory = DB::table('inventory_allocations')
            ->join('slots', 'inventory_allocations.slot_id', '=', 'slots.id')
            ->join('trays', 'slots.tray_id', '=', 'trays.id')
            ->where('trays.branch_id', $branch->id)
            ->sum('inventory_allocations.current_quantity');
        
        return [
            'total_trays' => $branch->trays()->count(),
            'total_slots' => $branch->getTotalSlotsCount(),
            'occupied_slots' => $branch->getOccupiedSlotsCount(),
            'available_slots' => $branch->getTotalSlotsCount() - $branch->getOccupiedSlotsCount(),
            'total_capacity' => $totalCapacity,
            'current_inventory' => $currentInventory,
            'current_utilization' => $branch->getCurrentInventoryUtilization(),
            'trays' => $branch->trays()->with(['slots.inventoryAllocation.menuItem'])->get()
        ];
    }

    /**
     * Get menu item inventory summary
     */
    public function getMenuItemInventorySummary(MenuItem $menuItem): array
    {
        return [
            'total_inventory' => $menuItem->getTotalInventory(),
            'total_capacity' => $menuItem->getTotalAllocatedCapacity(),
            'remaining_capacity' => $menuItem->getRemainingCapacity(),
            'utilization' => $menuItem->getInventoryUtilization(),
            'is_low_on_inventory' => $menuItem->isLowOnInventory(),
            'is_out_of_stock' => $menuItem->isOutOfStock(),
            'allocated_slots' => $menuItem->inventoryAllocations()->with('slot.tray')->get()
        ];
    }

    /**
     * Validate configuration changes for potential data loss
     */
    public function validateConfigurationChange(string $changeType, $model, array $newData): array
    {
        $warnings = [];

        switch ($changeType) {
            case 'delete_tray':
                if ($model instanceof Tray && $model->hasOccupiedSlots()) {
                    $warnings[] = 'This tray has occupied slots. Deleting it will remove all inventory data.';
                }
                break;

            case 'delete_slot':
                if ($model instanceof Slot && $model->isOccupied()) {
                    $warnings[] = 'This slot is occupied. Deleting it will remove inventory allocation data.';
                }
                break;

            case 'reduce_slot_capacity':
                if ($model instanceof Slot && isset($newData['capacity'])) {
                    $currentQuantity = $model->getCurrentQuantity();
                    if ($newData['capacity'] < $currentQuantity) {
                        $unitsToRemove = $currentQuantity - $newData['capacity'];
                        $warnings[] = "Reducing capacity to {$newData['capacity']} would require removing {$unitsToRemove} units of inventory.";
                    }
                }
                break;
        }

        return $warnings;
    }
}