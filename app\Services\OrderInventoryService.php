<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\MenuItem;
use App\Models\Branch;
use App\Models\InventoryAllocation;
use App\Exceptions\Inventory\InsufficientCapacityException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class OrderInventoryService
{
    protected BranchInventoryService $inventoryService;

    public function __construct(BranchInventoryService $inventoryService)
    {
        $this->inventoryService = $inventoryService;
    }

    /**
     * Check inventory availability for order items
     */
    public function checkInventoryAvailability(Branch $branch, array $orderItems): array
    {
        $availabilityResults = [];
        
        foreach ($orderItems as $item) {
            $menuItemId = $item['menu_item_id'];
            $requestedQuantity = $item['quantity'];
            
            $menuItem = MenuItem::find($menuItemId);
            if (!$menuItem) {
                $availabilityResults[$menuItemId] = [
                    'available' => false,
                    'reason' => 'Menu item not found',
                    'available_quantity' => 0,
                    'requested_quantity' => $requestedQuantity
                ];
                continue;
            }

            $totalInventory = $menuItem->getTotalInventory();
            $isAvailable = $totalInventory >= $requestedQuantity;

            $availabilityResults[$menuItemId] = [
                'available' => $isAvailable,
                'reason' => $isAvailable ? 'Available' : 'Insufficient inventory',
                'available_quantity' => $totalInventory,
                'requested_quantity' => $requestedQuantity,
                'menu_item' => $menuItem
            ];
        }

        return $availabilityResults;
    }

    /**
     * Reserve inventory for an order (deduct from available inventory)
     */
    public function reserveInventoryForOrder(Order $order): bool
    {
        return DB::transaction(function () use ($order) {
            $reservationResults = [];

            foreach ($order->items as $orderItem) {
                $result = $this->reserveInventoryForOrderItem($orderItem);
                $reservationResults[] = $result;
                
                if (!$result['success']) {
                    throw new InsufficientCapacityException(
                        "Insufficient inventory for menu item: {$orderItem->menuItem->name}. " .
                        "Requested: {$orderItem->quantity}, Available: {$result['available_quantity']}"
                    );
                }
            }

            // Mark order as having inventory reserved
            $order->update(['inventory_reserved' => true]);

            // Fire event for real-time updates
            event(new \App\Events\Inventory\OrderInventoryReserved($order, $reservationResults));

            return true;
        });
    }

    /**
     * Reserve inventory for a single order item
     */
    protected function reserveInventoryForOrderItem(OrderItem $orderItem): array
    {
        $menuItem = $orderItem->menuItem;
        $requestedQuantity = $orderItem->quantity;
        
        // Get all inventory allocations for this menu item, ordered by quantity (FIFO)
        $allocations = $menuItem->inventoryAllocations()
            ->with('slot')
            ->where('current_quantity', '>', 0)
            ->orderBy('current_quantity', 'desc') // Start with slots that have more inventory
            ->get();

        $totalAvailable = $allocations->sum('current_quantity');
        
        if ($totalAvailable < $requestedQuantity) {
            return [
                'success' => false,
                'available_quantity' => $totalAvailable,
                'requested_quantity' => $requestedQuantity,
                'message' => 'Insufficient inventory'
            ];
        }

        $remainingToReserve = $requestedQuantity;
        $reservedFromSlots = [];

        foreach ($allocations as $allocation) {
            if ($remainingToReserve <= 0) {
                break;
            }

            $availableInSlot = $allocation->current_quantity;
            $toReserveFromSlot = min($remainingToReserve, $availableInSlot);

            // Deduct from inventory allocation
            $allocation->removeInventory($toReserveFromSlot);
            
            $reservedFromSlots[] = [
                'slot_id' => $allocation->slot_id,
                'quantity_reserved' => $toReserveFromSlot,
                'allocation_id' => $allocation->id
            ];

            $remainingToReserve -= $toReserveFromSlot;
        }

        // Store reservation details in order item for potential restoration
        $orderItem->update([
            'inventory_reservations' => json_encode($reservedFromSlots)
        ]);

        return [
            'success' => true,
            'reserved_quantity' => $requestedQuantity,
            'reserved_from_slots' => $reservedFromSlots
        ];
    }

    /**
     * Restore inventory when an order is cancelled
     */
    public function restoreInventoryForOrder(Order $order): bool
    {
        return DB::transaction(function () use ($order) {
            foreach ($order->items as $orderItem) {
                $this->restoreInventoryForOrderItem($orderItem);
            }

            // Mark order as having inventory restored
            $order->update(['inventory_reserved' => false]);

            // Fire event for real-time updates
            event(new \App\Events\Inventory\OrderInventoryRestored($order, []));

            return true;
        });
    }

    /**
     * Restore inventory for a single order item
     */
    protected function restoreInventoryForOrderItem(OrderItem $orderItem): bool
    {
        $reservations = json_decode($orderItem->inventory_reservations ?? '[]', true);
        
        if (empty($reservations)) {
            return true; // Nothing to restore
        }

        foreach ($reservations as $reservation) {
            $allocation = InventoryAllocation::find($reservation['allocation_id']);
            
            if ($allocation) {
                // Add back the reserved quantity
                $allocation->addInventory($reservation['quantity_reserved']);
            }
        }

        // Clear reservation data
        $orderItem->update(['inventory_reservations' => null]);

        return true;
    }

    /**
     * Get inventory impact summary for an order
     */
    public function getOrderInventoryImpact(Order $order): array
    {
        $impact = [
            'total_items' => $order->items->count(),
            'items_with_inventory_impact' => 0,
            'total_inventory_reserved' => 0,
            'items_detail' => []
        ];

        foreach ($order->items as $orderItem) {
            $menuItem = $orderItem->menuItem;
            $currentInventory = $menuItem->getTotalInventory();
            $requestedQuantity = $orderItem->quantity;

            $itemImpact = [
                'menu_item_id' => $menuItem->id,
                'menu_item_name' => $menuItem->name,
                'requested_quantity' => $requestedQuantity,
                'current_inventory' => $currentInventory,
                'inventory_sufficient' => $currentInventory >= $requestedQuantity,
                'inventory_after_order' => max(0, $currentInventory - $requestedQuantity)
            ];

            if ($requestedQuantity > 0) {
                $impact['items_with_inventory_impact']++;
                $impact['total_inventory_reserved'] += $requestedQuantity;
            }

            $impact['items_detail'][] = $itemImpact;
        }

        return $impact;
    }

    /**
     * Get low inventory alerts for a branch
     */
    public function getLowInventoryAlerts(Branch $branch, int $threshold = 5): Collection
    {
        return $branch->trays()
            ->with(['slots.inventoryAllocation.menuItem'])
            ->get()
            ->flatMap(function ($tray) {
                return $tray->slots;
            })
            ->filter(function ($slot) use ($threshold) {
                $allocation = $slot->inventoryAllocation;
                return $allocation && $allocation->current_quantity <= $threshold && $allocation->current_quantity > 0;
            })
            ->map(function ($slot) {
                $allocation = $slot->inventoryAllocation;
                return [
                    'slot_id' => $slot->id,
                    'slot_name' => $slot->name,
                    'tray_name' => $slot->tray->name,
                    'menu_item_name' => $allocation->menuItem->name,
                    'current_quantity' => $allocation->current_quantity,
                    'capacity' => $slot->capacity,
                    'utilization' => $allocation->getCapacityUtilization()
                ];
            });
    }

    /**
     * Get out of stock items for a branch
     */
    public function getOutOfStockItems(Branch $branch): Collection
    {
        return $branch->trays()
            ->with(['slots.inventoryAllocation.menuItem'])
            ->get()
            ->flatMap(function ($tray) {
                return $tray->slots;
            })
            ->filter(function ($slot) {
                $allocation = $slot->inventoryAllocation;
                return $allocation && $allocation->current_quantity === 0;
            })
            ->map(function ($slot) {
                $allocation = $slot->inventoryAllocation;
                return [
                    'slot_id' => $slot->id,
                    'slot_name' => $slot->name,
                    'tray_name' => $slot->tray->name,
                    'menu_item_name' => $allocation->menuItem->name,
                    'menu_item_id' => $allocation->menu_item_id
                ];
            });
    }

    /**
     * Validate order against current inventory before processing
     */
    public function validateOrderInventory(array $orderItems, Branch $branch): array
    {
        $validation = [
            'valid' => true,
            'errors' => [],
            'warnings' => []
        ];

        $availabilityResults = $this->checkInventoryAvailability($branch, $orderItems);

        foreach ($availabilityResults as $menuItemId => $result) {
            if (!$result['available']) {
                $validation['valid'] = false;
                $validation['errors'][] = [
                    'menu_item_id' => $menuItemId,
                    'message' => $result['reason'],
                    'available_quantity' => $result['available_quantity'],
                    'requested_quantity' => $result['requested_quantity']
                ];
            } elseif ($result['available_quantity'] <= 5) {
                $validation['warnings'][] = [
                    'menu_item_id' => $menuItemId,
                    'message' => 'Low inventory warning',
                    'available_quantity' => $result['available_quantity'],
                    'requested_quantity' => $result['requested_quantity']
                ];
            }
        }

        return $validation;
    }
}