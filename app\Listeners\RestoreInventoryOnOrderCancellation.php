<?php

namespace App\Listeners;

use App\Events\OrderCancelled;
use App\Services\OrderInventoryService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class RestoreInventoryOnOrderCancellation implements ShouldQueue
{
    use InteractsWithQueue;

    protected OrderInventoryService $orderInventoryService;

    /**
     * Create the event listener.
     */
    public function __construct(OrderInventoryService $orderInventoryService)
    {
        $this->orderInventoryService = $orderInventoryService;
    }

    /**
     * Handle the event.
     */
    public function handle(OrderCancelled $event): void
    {
        $order = $event->order;

        // Only restore inventory if it was previously reserved
        if (!$order->inventory_reserved) {
            Log::info('Order inventory was not reserved, skipping restoration', [
                'order_id' => $order->id,
                'order_number' => $order->order_number
            ]);
            return;
        }

        try {
            $this->orderInventoryService->restoreInventoryForOrder($order);
            
            Log::info('Successfully restored inventory for cancelled order', [
                'order_id' => $order->id,
                'order_number' => $order->order_number
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to restore inventory for cancelled order', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Optionally, you could dispatch a notification to administrators
            // about the failed inventory restoration
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(OrderCancelled $event, \Throwable $exception): void
    {
        Log::error('RestoreInventoryOnOrderCancellation listener failed', [
            'order_id' => $event->order->id,
            'order_number' => $event->order->order_number,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}