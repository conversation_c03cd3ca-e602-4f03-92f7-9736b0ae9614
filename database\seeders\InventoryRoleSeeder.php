<?php

namespace Database\Seeders;

use App\Models\Module;
use App\Models\Restaurant;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class InventoryRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get inventory module
        $inventoryModule = Module::where('name', 'Inventory')->first();
        
        if (!$inventoryModule) {
            $this->command->error('Inventory module not found. Please run ModuleSeeder first.');
            return;
        }

        // Get all inventory permissions
        $inventoryPermissions = Permission::where('module_id', $inventoryModule->id)->pluck('name')->toArray();
        
        if (empty($inventoryPermissions)) {
            $this->command->error('No inventory permissions found. Please run PermissionSeeder first.');
            return;
        }

        // Create roles for each restaurant
        $restaurants = Restaurant::all();
        
        foreach ($restaurants as $restaurant) {
            // Branch Manager Role - Full inventory access
            $branchManagerRole = Role::firstOrCreate([
                'name' => 'Branch Manager_' . $restaurant->id,
                'guard_name' => 'web',
                'restaurant_id' => $restaurant->id
            ], [
                'display_name' => 'Branch Manager'
            ]);
            
            // Give all inventory permissions to branch manager
            $branchManagerRole->syncPermissions($inventoryPermissions);

            // Inventory Manager Role - Configuration and allocation access
            $inventoryManagerRole = Role::firstOrCreate([
                'name' => 'Inventory Manager_' . $restaurant->id,
                'guard_name' => 'web',
                'restaurant_id' => $restaurant->id
            ], [
                'display_name' => 'Inventory Manager'
            ]);
            
            // Give specific permissions to inventory manager
            $inventoryManagerPermissions = [
                'View Inventory Dashboard',
                'Configure Inventory',
                'Manage Inventory Allocation',
                'Create Tray',
                'Update Tray',
                'Create Slot',
                'Update Slot',
                'Allocate Inventory',
                'Adjust Inventory'
            ];
            
            $inventoryManagerRole->syncPermissions($inventoryManagerPermissions);

            // Staff Role - View only access
            $staffRole = Role::firstOrCreate([
                'name' => 'Staff_' . $restaurant->id,
                'guard_name' => 'web',
                'restaurant_id' => $restaurant->id
            ], [
                'display_name' => 'Staff'
            ]);
            
            // Give view-only permissions to staff
            $staffPermissions = [
                'View Inventory Dashboard'
            ];
            
            $staffRole->syncPermissions($staffPermissions);

            $this->command->info("Created inventory roles for restaurant: {$restaurant->name}");
        }
    }
}