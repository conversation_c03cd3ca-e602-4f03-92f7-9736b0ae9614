<?php

namespace Database\Seeders;

use App\Models\EmailSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EmailSettingSeeder extends Seeder
{

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        EmailSetting::create([
            'mail_from_name' => config('app.name'),
            'mail_from_email' => '<EMAIL>',
            'smtp_host' => 'smtp.zoho.com',
	    'smtp_port' => '587',
            'mail_driver' => 'smtp',
            'smtp_encryption' => 'tls',
            'mail_username' => '<EMAIL>',
            'enable_queue' => 'no',
        ]);
    }

}
