<?php

return array (
  'menuNamePlaceholder' => 'e.g. Breakfast',
  'searchMenus' => 'Search items here',
  'searchMenuItems' => 'Search your item here',
  'menuItemNamePlaceholder' => 'e.g., chips',
  'itemVariationPlaceholder' => 'e.g., Small',
  'itemDescriptionPlaceholder' => 'e.g., A classic bag of potato chips.',
  'categoryNamePlaceholder' => 'e.g., Drinks',
  'searchItemCategory' => 'Search your item category here',
  'searchCustomers' => 'Search by name, email or phone number',
  'searchStaffmember' => 'Search by name or email',
  'searchPackages' => 'Search your package here',
  'businessNamePlaceHolder' => 'e.g., The Vending Co.',
  'searchPayments' => 'Search payments by amount, method, TX ID',
  'searchDuePayments' => 'Search payments by amount or order #',
  'methodExamples' => 'e.g., Cash, Cheque, Bank Transfer, etc.',
  'methodDescription' => 'e.g., via USD dollar, etc.',
  'facebookPlaceHolder' => 'Enter your Facebook URL',
  'instagramPlaceHolder' => 'Enter your Instagram URL',
  'twitterPlaceHolder' => 'Enter your Twitter handle',
  'yelpPlaceHolder' => 'Enter your Yelp URL',
  'metaKeywordPlaceHolder' => 'Enter Comma Saperated keyword',
  'metaDescriptionPlaceHolder' => 'Enter Meta Description',
  'languageCodePlaceholder' => 'e.g., en, ar, fr, etc.',
  'languageNamePlaceholder' => 'e.g., English, Arabic, French, etc.',
  'flagCodePlaceholder' => 'e.g., us, gb, sa, etc.',
  'purchaseCode' => 'e.g. 147778a2-dfa2-424e-a29f-xxxxxxxxx',
  'addTitle' => 'Add Expense Title',
  'addDescription' => 'Add Expense Description',
  'addAmount' => 'Add Amount',
  'expense' => 'Expense',
  'vendors' => 'Vendors',
  'vendorName' => 'Vendor Name',
  'vendorEmail' => 'Vendor Email',
  'vendorPhone' => 'Vendor Phone',
  'vendorAddress' => 'Vendor Address',
  'contactPerson' => 'Contact Person',
  'expensesCategory' => 'Expenses Category',
  'searchMenuItem' => 'Search for Item',
  'customerName' => 'Add Customer Name',
  'customerEmail' => 'Add Customer Email',
  'customerPhone' => 'Add Customer Phone',
  'customerAddress' => 'Add Customer Address',
  'metaTtilePlaceHolder' => 'Enter Meta Title',
  'menuNamePlaceHolder' => 'Add  Name',
  'menuSlugPlaceHolder' => 'Add Slug',
  'menuContentPlaceHolder' => 'Add  Content',
  'enterCustomAmountPlaceholder' => 'Enter custom amount',
  'addNotePlaceholder' => 'Add a note with your tip...',
  'addOrderNotesPlaceholder' => 'Add order notes here...',
  'enterExpenseCategoryName' => 'Enter Category Name',
  'enterExpenseCategoryDescription' => 'Enter Category Description',
  'addPrinterName' => 'Add Printer Name',
  'addPrinterSharedName' => 'Add Printer Name',
  'IpAddress' => 'Add Printer IP Address',
  'portAddress' => 'Add Printer Port Address',
  'ipv4Addres' => 'Add Printer IPv4 Address',
  'enterGoogleMapApiKey' => 'Enter Google Map API Key e.g., AIzaSyD-xxxxxxxxxxxxxxxxx',
  'addressLabelPlaceholder' => 'e.g., home, office, etc.',
  'addressPlaceholder' => 'e.g., 123 Main St, City, Country',
  'searchProductOrCategory' => 'Search product or category here',
);
