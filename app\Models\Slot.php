<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Traits\BranchScoped;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Slot extends BaseModel
{
    use HasFactory, BranchScoped;

    protected $fillable = [
        'tray_id',
        'name',
        'capacity',
        'sort_order'
    ];

    protected $casts = [
        'capacity' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Validation rules for slot creation and updates
     */
    public static function validationRules($slotId = null): array
    {
        return [
            'tray_id' => 'required|exists:trays,id',
            'name' => [
                'required',
                'string',
                'max:255',
                'unique:slots,name,' . $slotId . ',id,tray_id,' . request('tray_id')
            ],
            'capacity' => 'required|integer|min:1',
            'sort_order' => 'nullable|integer|min:0'
        ];
    }

    /**
     * Get validation messages
     */
    public static function validationMessages(): array
    {
        return [
            'tray_id.required' => 'Tray is required.',
            'tray_id.exists' => 'Selected tray does not exist.',
            'name.required' => 'Slot name is required.',
            'name.string' => 'Slot name must be a string.',
            'name.max' => 'Slot name cannot exceed 255 characters.',
            'name.unique' => 'A slot with this name already exists in this tray.',
            'capacity.required' => 'Capacity is required.',
            'capacity.integer' => 'Capacity must be an integer.',
            'capacity.min' => 'Capacity must be at least 1.',
            'sort_order.integer' => 'Sort order must be an integer.',
            'sort_order.min' => 'Sort order cannot be negative.'
        ];
    }

    /**
     * Get the tray that owns this slot
     */
    public function tray(): BelongsTo
    {
        return $this->belongsTo(Tray::class);
    }

    /**
     * Get the inventory allocation for this slot
     */
    public function inventoryAllocation(): HasOne
    {
        return $this->hasOne(InventoryAllocation::class);
    }

    /**
     * Check if this slot is occupied by a menu item
     */
    public function isOccupied(): bool
    {
        return $this->inventoryAllocation()->exists();
    }

    /**
     * Get the current quantity in this slot
     */
    public function getCurrentQuantity(): int
    {
        $allocation = $this->inventoryAllocation;
        return $allocation ? $allocation->current_quantity : 0;
    }

    /**
     * Get remaining capacity in this slot
     */
    public function getRemainingCapacity(): int
    {
        return $this->capacity - $this->getCurrentQuantity();
    }

    /**
     * Check if slot can accommodate additional quantity
     */
    public function canAddQuantity(int $quantity): bool
    {
        return $this->getRemainingCapacity() >= $quantity;
    }

    /**
     * Get capacity utilization percentage
     */
    public function getCapacityUtilization(): float
    {
        if ($this->capacity === 0) {
            return 0.0;
        }
        
        return ($this->getCurrentQuantity() / $this->capacity) * 100;
    }

    /**
     * Check if slot is near capacity (80% or more)
     */
    public function isNearCapacity(): bool
    {
        return $this->getCapacityUtilization() >= 80.0;
    }

    /**
     * Check if slot is at full capacity
     */
    public function isAtCapacity(): bool
    {
        return $this->getCurrentQuantity() >= $this->capacity;
    }

    /**
     * Get the menu item assigned to this slot (if any)
     */
    public function getAssignedMenuItem(): ?MenuItem
    {
        $allocation = $this->inventoryAllocation;
        return $allocation ? $allocation->menuItem : null;
    }
}