# Inventory Exception Classes

This directory contains custom exception classes for the branch inventory management system. These exceptions provide structured error handling with user-friendly messages and detailed error information.

## Exception Classes

### SlotOccupiedException

Thrown when attempting to allocate a menu item to a slot that is already occupied by a different menu item.

**Usage:**
```php
throw new SlotOccupiedException($slotId, $currentMenuItemId, $attemptedMenuItemId);
```

**Properties:**
- `getSlotId()`: Returns the ID of the occupied slot
- `getCurrentMenuItemId()`: Returns the ID of the menu item currently in the slot
- `getAttemptedMenuItemId()`: Returns the ID of the menu item that was attempted to be allocated
- `getUserFriendlyMessage()`: Returns a user-friendly error message

### InsufficientCapacityException

Thrown when attempting to add inventory that would exceed the slot's capacity.

**Usage:**
```php
throw new InsufficientCapacityException($slotId, $requestedQuantity, $availableCapacity, $currentQuantity);
```

**Properties:**
- `getSlotId()`: Returns the ID of the slot
- `getRequestedQuantity()`: Returns the quantity that was requested to be added
- `getAvailableCapacity()`: Returns the remaining capacity in the slot
- `getCurrentQuantity()`: Returns the current quantity in the slot
- `getUserFriendlyMessage()`: Returns a user-friendly error message

### InvalidAllocationException

Thrown when an allocation operation violates business rules.

**Usage:**
```php
throw new InvalidAllocationException($violationType, $details);
```

**Violation Types:**
- `VIOLATION_NO_SLOTS`: Menu item must be allocated to at least one slot
- `VIOLATION_INVALID_QUANTITY`: Quantity must be a positive integer
- `VIOLATION_MENU_ITEM_NOT_FOUND`: Menu item not found or not accessible
- `VIOLATION_SLOT_NOT_FOUND`: Slot not found or not accessible
- `VIOLATION_BRANCH_MISMATCH`: Menu item and slot must belong to the same branch

**Properties:**
- `getViolationType()`: Returns the type of violation
- `getDetails()`: Returns additional details about the violation
- `getMenuItemId()`: Returns the menu item ID if applicable
- `getUserFriendlyMessage()`: Returns a user-friendly error message

### ConfigurationChangeException

Thrown when configuration changes would result in data loss and require user confirmation.

**Usage:**
```php
throw new ConfigurationChangeException($changeType, $affectedData, $requiresConfirmation);
```

**Change Types:**
- `CHANGE_REDUCE_TRAYS`: Reducing the number of trays
- `CHANGE_REDUCE_SLOTS`: Reducing the number of slots
- `CHANGE_REDUCE_CAPACITY`: Reducing slot capacity below current inventory
- `CHANGE_DELETE_TRAY`: Deleting a tray with existing slots
- `CHANGE_DELETE_SLOT`: Deleting a slot with existing inventory

**Properties:**
- `getChangeType()`: Returns the type of configuration change
- `getAffectedData()`: Returns data about what will be affected
- `requiresConfirmation()`: Returns whether user confirmation is required
- `getUserFriendlyMessage()`: Returns a user-friendly warning message
- `getConfirmationPrompt()`: Returns a confirmation prompt for the user

## Exception Handler

The `InventoryExceptionHandler` class provides centralized handling for all inventory exceptions:

**Usage:**
```php
try {
    // Inventory operation
} catch (\Exception $e) {
    return InventoryExceptionHandler::handle($e, $request);
}
```

**Methods:**
- `handle(\Exception $exception, Request $request = null)`: Handles any inventory exception and returns appropriate response
- `getUserFriendlyMessage(\Exception $exception)`: Gets user-friendly message for any exception
- `requiresConfirmation(\Exception $exception)`: Checks if exception requires user confirmation

## Example Usage in Services

```php
class BranchInventoryService
{
    public function allocateMenuItemToSlot(int $slotId, int $menuItemId, int $quantity)
    {
        $slot = Slot::findOrFail($slotId);
        
        // Check if slot is already occupied
        if ($slot->isOccupied() && $slot->inventoryAllocation->menu_item_id !== $menuItemId) {
            throw new SlotOccupiedException(
                $slotId,
                $slot->inventoryAllocation->menu_item_id,
                $menuItemId
            );
        }
        
        // Check capacity
        if ($slot->getRemainingCapacity() < $quantity) {
            throw new InsufficientCapacityException(
                $slotId,
                $quantity,
                $slot->getRemainingCapacity(),
                $slot->inventoryAllocation->current_quantity ?? 0
            );
        }
        
        // Perform allocation...
    }
}
```

## Example Usage in Controllers

```php
class InventoryController extends Controller
{
    public function allocateInventory(Request $request)
    {
        try {
            $this->inventoryService->allocateMenuItemToSlot(
                $request->slot_id,
                $request->menu_item_id,
                $request->quantity
            );
            
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return InventoryExceptionHandler::handle($e, $request);
        }
    }
}
```

## Testing

All exception classes have comprehensive unit tests located in `tests/Unit/Exceptions/Inventory/`. The tests cover:

- Exception instantiation and property access
- Message generation (both technical and user-friendly)
- Custom message handling
- Exception inheritance
- Handler functionality

Run the tests with:
```bash
php artisan test tests/Unit/Exceptions/Inventory/
```