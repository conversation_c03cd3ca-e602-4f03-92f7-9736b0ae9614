<?php

namespace Database\Seeders;

use App\Models\Branch;
use App\Models\MenuItem;
use App\Models\Tray;
use App\Models\Slot;
use App\Models\InventoryAllocation;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

class InventoryDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(Branch $branch = null): void
    {
        DB::transaction(function () use ($branch) {
            if ($branch) {
                $this->createInventoryForBranch($branch);
	    } else {
               \Log::error( "No Branch specified");
            }
        });
    }

    

    /**
     * Create comprehensive inventory setup for a branch
     */
    private function createInventoryForBranch(Branch $branch): void
    {

	 Artisan::call('seed:trayslots', [
             '--b' => $branch->id,
             '--t' => 3,
             '--s' => ['a=10', '5'],
             '--c' => ['a1=5', 'a*=10', '5'],
	 ]); 

        // Allocate menu items to some slots
        $this->allocateMenuItemsToSlots($branch);
    }


    /**
     * Allocate menu items to available slots
     */
     private function allocateMenuItemsToSlots(Branch $branch): void
     {
         // Get available menu items (create some if none exist)
         $menuItems = MenuItem::where('branch_id', $branch->id)->get();
     
         // Get all slots for this branch
         $slots = Slot::whereHas('tray', function ($query) use ($branch) {
             $query->where('branch_id', $branch->id);
         })->get();
     
         // Allocate menu items to random slots (about 70% of slots)
         $slotsToAllocate = $slots->random((int)($slots->count() * 0.7));
     
         foreach ($slotsToAllocate as $slot) {
             $menuItem = $menuItems->random();
     
             // Create allocation manually with realistic quantity
             \App\Models\InventoryAllocation::create([
                 'slot_id' => $slot->id,
                 'menu_item_id' => $menuItem->id,
                 'current_quantity' => rand(1, $slot->capacity), // or any logic
             ]);
         }
     
     }
    
}
