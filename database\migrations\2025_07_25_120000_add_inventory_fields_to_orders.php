<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->boolean('inventory_reserved')->default(false)->after('status');
        });

        Schema::table('order_items', function (Blueprint $table) {
            $table->json('inventory_reservations')->nullable()->after('amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('inventory_reserved');
        });

        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn('inventory_reservations');
        });
    }
};