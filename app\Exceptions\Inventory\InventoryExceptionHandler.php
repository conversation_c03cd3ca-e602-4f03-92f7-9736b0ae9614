<?php

namespace App\Exceptions\Inventory;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class InventoryExceptionHandler
{
    /**
     * Handle inventory-related exceptions and return appropriate responses
     */
    public static function handle(\Exception $exception, Request $request = null): JsonResponse|RedirectResponse|array
    {
        if ($exception instanceof SlotOccupiedException) {
            return self::handleSlotOccupiedException($exception, $request);
        }

        if ($exception instanceof InsufficientCapacityException) {
            return self::handleInsufficientCapacityException($exception, $request);
        }

        if ($exception instanceof InvalidAllocationException) {
            return self::handleInvalidAllocationException($exception, $request);
        }

        if ($exception instanceof ConfigurationChangeException) {
            return self::handleConfigurationChangeException($exception, $request);
        }

        // Fallback for unknown inventory exceptions
        return self::handleGenericInventoryException($exception, $request);
    }

    private static function handleSlotOccupiedException(SlotOccupiedException $exception, ?Request $request): JsonResponse|RedirectResponse|array
    {
        $data = [
            'error' => 'slot_occupied',
            'message' => $exception->getUserFriendlyMessage(),
            'slot_id' => $exception->getSlotId(),
            'current_menu_item_id' => $exception->getCurrentMenuItemId(),
            'attempted_menu_item_id' => $exception->getAttemptedMenuItemId(),
        ];

        if ($request && $request->expectsJson()) {
            return response()->json($data, 409); // Conflict
        }

        if ($request) {
            return redirect()->back()
                ->withErrors(['allocation' => $exception->getUserFriendlyMessage()])
                ->withInput();
        }

        return $data;
    }

    private static function handleInsufficientCapacityException(InsufficientCapacityException $exception, ?Request $request): JsonResponse|RedirectResponse|array
    {
        $data = [
            'error' => 'insufficient_capacity',
            'message' => $exception->getUserFriendlyMessage(),
            'slot_id' => $exception->getSlotId(),
            'requested_quantity' => $exception->getRequestedQuantity(),
            'available_capacity' => $exception->getAvailableCapacity(),
            'current_quantity' => $exception->getCurrentQuantity(),
        ];

        if ($request && $request->expectsJson()) {
            return response()->json($data, 400); // Bad Request
        }

        if ($request) {
            return redirect()->back()
                ->withErrors(['capacity' => $exception->getUserFriendlyMessage()])
                ->withInput();
        }

        return $data;
    }

    private static function handleInvalidAllocationException(InvalidAllocationException $exception, ?Request $request): JsonResponse|RedirectResponse|array
    {
        $data = [
            'error' => 'invalid_allocation',
            'message' => $exception->getUserFriendlyMessage(),
            'violation_type' => $exception->getViolationType(),
            'details' => $exception->getDetails(),
        ];

        if ($request && $request->expectsJson()) {
            return response()->json($data, 422); // Unprocessable Entity
        }

        if ($request) {
            return redirect()->back()
                ->withErrors(['allocation' => $exception->getUserFriendlyMessage()])
                ->withInput();
        }

        return $data;
    }

    private static function handleConfigurationChangeException(ConfigurationChangeException $exception, ?Request $request): JsonResponse|RedirectResponse|array
    {
        $data = [
            'error' => 'configuration_change_required',
            'message' => $exception->getUserFriendlyMessage(),
            'change_type' => $exception->getChangeType(),
            'affected_data' => $exception->getAffectedData(),
            'requires_confirmation' => $exception->requiresConfirmation(),
            'confirmation_prompt' => $exception->getConfirmationPrompt(),
        ];

        if ($request && $request->expectsJson()) {
            return response()->json($data, 409); // Conflict
        }

        if ($request) {
            return redirect()->back()
                ->withErrors(['configuration' => $exception->getUserFriendlyMessage()])
                ->with('confirmation_required', $data)
                ->withInput();
        }

        return $data;
    }

    private static function handleGenericInventoryException(\Exception $exception, ?Request $request): JsonResponse|RedirectResponse|array
    {
        $data = [
            'error' => 'inventory_error',
            'message' => 'An inventory operation failed. Please try again.',
            'details' => $exception->getMessage(),
        ];

        if ($request && $request->expectsJson()) {
            return response()->json($data, 500); // Internal Server Error
        }

        if ($request) {
            return redirect()->back()
                ->withErrors(['inventory' => 'An inventory operation failed. Please try again.'])
                ->withInput();
        }

        return $data;
    }

    /**
     * Get user-friendly error message for any inventory exception
     */
    public static function getUserFriendlyMessage(\Exception $exception): string
    {
        if (method_exists($exception, 'getUserFriendlyMessage')) {
            return $exception->getUserFriendlyMessage();
        }

        return 'An inventory operation failed. Please try again.';
    }

    /**
     * Check if an exception requires user confirmation
     */
    public static function requiresConfirmation(\Exception $exception): bool
    {
        if ($exception instanceof ConfigurationChangeException) {
            return $exception->requiresConfirmation();
        }

        return false;
    }
}